{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160771704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "ETag", "Value": "W/\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "27232"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:57:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON><PERSON>jwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE="}]}, {"Route": "app.zd1wu8imt9.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160771704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "ETag", "Value": "W/\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.zd1wu8imt9.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "27232"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:57:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.zd1wu8imt9.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "integrity", "Value": "sha256-<PERSON><PERSON>jwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "index.hogd10aeqz.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000381679389"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "ETag", "Value": "W/\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.hogd10aeqz.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13608"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:14:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.hogd10aeqz.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "integrity", "Value": "sha256-D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000381679389"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "ETag", "Value": "W/\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13608"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:14:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o="}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000171556013"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo=\""}, {"Name": "ETag", "Value": "W/\"y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36566"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo="}]}, {"Route": "styles.z65ersqd57.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000171556013"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo=\""}, {"Name": "ETag", "Value": "W/\"y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z65ersqd57"}, {"Name": "integrity", "Value": "sha256-y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.z65ersqd57.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "36566"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z65ersqd57"}, {"Name": "integrity", "Value": "sha256-y9y5CaYm2g4ylFfjO7dLOxpzaSEY7AnDMgRtOa0Oi6s="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.z65ersqd57.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 12:28:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z65ersqd57"}, {"Name": "integrity", "Value": "sha256-oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo="}, {"Name": "label", "Value": "styles.css.gz"}]}]}