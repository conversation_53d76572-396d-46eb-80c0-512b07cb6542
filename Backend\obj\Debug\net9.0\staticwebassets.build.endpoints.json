{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160771704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "ETag", "Value": "W/\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "27232"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:57:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON><PERSON>jwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE="}]}, {"Route": "app.zd1wu8imt9.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160771704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "ETag", "Value": "W/\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.zd1wu8imt9.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "27232"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:57:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.zd1wu8imt9.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "integrity", "Value": "sha256-<PERSON><PERSON>jwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "index.hogd10aeqz.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000381679389"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "ETag", "Value": "W/\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.hogd10aeqz.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13608"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:55:35 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.hogd10aeqz.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "integrity", "Value": "sha256-D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000381679389"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "ETag", "Value": "W/\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13608"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:55:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o="}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000180799132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "ETag", "Value": "W/\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33647"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:54:59 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8="}]}, {"Route": "styles.iq6iwg8y1j.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000180799132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "ETag", "Value": "W/\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iq6iwg8y1j"}, {"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.iq6iwg8y1j.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33647"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:54:59 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iq6iwg8y1j"}, {"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.iq6iwg8y1j.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iq6iwg8y1j"}, {"Name": "integrity", "Value": "sha256-xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8="}, {"Name": "label", "Value": "styles.css.gz"}]}]}