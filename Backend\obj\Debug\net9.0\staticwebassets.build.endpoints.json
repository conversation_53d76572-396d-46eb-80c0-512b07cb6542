{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.6cmviv2ofs.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000321027287"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "ETag", "Value": "W/\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cmviv2ofs"}, {"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.6cmviv2ofs.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:55:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cmviv2ofs"}, {"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.6cmviv2ofs.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cmviv2ofs"}, {"Name": "integrity", "Value": "sha256-KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000321027287"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "ETag", "Value": "W/\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:55:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo="}]}, {"Route": "index.8em28t5j70.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000804505229"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "ETag", "Value": "W/\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8em28t5j70"}, {"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.8em28t5j70.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4626"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:53:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8em28t5j70"}, {"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.8em28t5j70.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8em28t5j70"}, {"Name": "integrity", "Value": "sha256-2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000804505229"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "ETag", "Value": "W/\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4626"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:53:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ="}]}, {"Route": "styles.1brjmvz4go.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000558659218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "ETag", "Value": "W/\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1brjmvz4go"}, {"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.1brjmvz4go.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6740"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:54:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1brjmvz4go"}, {"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.1brjmvz4go.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1brjmvz4go"}, {"Name": "integrity", "Value": "sha256-3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE="}, {"Name": "label", "Value": "styles.css.gz"}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000558659218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "ETag", "Value": "W/\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6740"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:54:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE="}]}]}