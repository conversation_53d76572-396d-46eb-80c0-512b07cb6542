<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArbFinder Pro - Advanced Sports Arbitrage</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-brand">
                <div class="brand-icon">⚡</div>
                <div class="brand-text">
                    <h1>ArbFinder</h1>
                    <span>Pro</span>
                </div>
            </div>
            <div class="nav-controls">
                <button id="refreshBtn" class="btn btn-primary">
                    <span class="btn-icon">🔍</span>
                    <span>Find Opportunities</span>
                </button>
                <button id="settingsBtn" class="btn btn-ghost">
                    <span class="btn-icon">⚙️</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Control Panel -->
            <aside class="control-panel">
                <!-- Sports Selection -->
                <div class="panel-section">
                    <div class="section-header">
                        <h3>🏆 Sports</h3>
                        <div class="section-badge" id="sportsBadge">0 selected</div>
                    </div>
                    <div class="sports-grid" id="sportsContainer">
                        <div class="loading-skeleton">
                            <div class="skeleton-item"></div>
                            <div class="skeleton-item"></div>
                            <div class="skeleton-item"></div>
                        </div>
                    </div>
                </div>

                <!-- Markets Selection -->
                <div class="panel-section">
                    <div class="section-header">
                        <h3>📊 Markets</h3>
                        <div class="section-badge" id="marketsBadge">0 selected</div>
                        <button class="select-all-btn" id="selectAllMarkets">Select All</button>
                    </div>
                    <div class="markets-grid" id="marketsContainer">
                        <div class="loading-skeleton">
                            <div class="skeleton-item"></div>
                            <div class="skeleton-item"></div>
                        </div>
                    </div>
                </div>

                <!-- Bookmakers Selection -->
                <div class="panel-section">
                    <div class="section-header">
                        <h3>🏪 Bookmakers</h3>
                        <div class="section-badge" id="bookmakersBadge">0 selected</div>
                        <button class="select-all-btn" id="selectAllBookmakers">Select All</button>
                    </div>
                    <div class="bookmakers-grid" id="bookmakersContainer">
                        <div class="loading-skeleton">
                            <div class="skeleton-item"></div>
                            <div class="skeleton-item"></div>
                            <div class="skeleton-item"></div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Results Dashboard -->
            <main class="results-dashboard">
                <div class="dashboard-header">
                    <div class="header-info">
                        <h2>💎 Arbitrage Opportunities</h2>
                        <div class="status-indicator">
                            <div class="status-dot" id="statusDot"></div>
                            <span id="statusText">Ready to discover opportunities</span>
                        </div>
                    </div>
                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <div class="stat-value" id="totalOpportunities">0</div>
                            <div class="stat-label">Total Events</div>
                        </div>
                        <div class="stat-card highlight">
                            <div class="stat-value" id="profitableArbs">0</div>
                            <div class="stat-label">Profitable Arbs</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="bestRoi">0%</div>
                            <div class="stat-label">Best ROI</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="lastUpdate">Never</div>
                            <div class="stat-label">Last Update</div>
                        </div>
                    </div>
                </div>

                <div id="resultsContainer" class="results-container">
                    <div class="welcome-state">
                        <div class="welcome-icon">🚀</div>
                        <h3>Welcome to ArbFinder Pro</h3>
                        <div class="welcome-steps">
                            <div class="step">
                                <div class="step-number">1</div>
                                <div class="step-text">Configure your API key in Settings</div>
                            </div>
                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-text">Select sports, markets, and bookmakers</div>
                            </div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <div class="step-text">Click "Find Opportunities" to start</div>
                            </div>
                        </div>
                        <button class="welcome-cta" onclick="document.getElementById('settingsModal').style.display='block'">
                            Get Started
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <div class="modal-icon">⚙️</div>
                    <h3>Settings</h3>
                </div>
                <button id="closeModal" class="close-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <div class="setting-item priority">
                        <div class="setting-header">
                            <div class="setting-icon">🔑</div>
                            <div class="setting-info">
                                <label for="apiKeyOverride">API Key</label>
                                <span class="setting-description">Required for accessing live betting odds and bookmaker data</span>
                            </div>
                        </div>
                        <div class="setting-content">
                            <input type="password" id="apiKeyOverride" placeholder="Enter your Odds API key" class="form-control" required>
                            <div class="setting-help">
                                🎯 Get your free API key at <a href="https://the-odds-api.com/" target="_blank">the-odds-api.com</a>
                                <br>🔒 Stored securely in your browser only - never sent to our servers
                                <br>📊 Free tier includes 500 requests per month
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-header">
                            <div class="setting-icon">💰</div>
                            <div class="setting-info">
                                <label for="capitalAmount">Trading Capital</label>
                                <span class="setting-description">Total amount available for arbitrage opportunities</span>
                            </div>
                        </div>
                        <div class="setting-content">
                            <div class="capital-input-group">
                                <div class="currency-symbol">$</div>
                                <input type="number" id="capitalAmount" min="1" max="1000000" value="100" class="form-control capital-input" placeholder="100">
                            </div>
                            <div class="capital-presets">
                                <button type="button" class="capital-preset" data-amount="100">$100</button>
                                <button type="button" class="capital-preset" data-amount="500">$500</button>
                                <button type="button" class="capital-preset" data-amount="1000">$1K</button>
                                <button type="button" class="capital-preset" data-amount="5000">$5K</button>
                                <button type="button" class="capital-preset" data-amount="10000">$10K</button>
                            </div>
                            <div class="setting-help">
                                💡 This amount will be used to calculate optimal stake distributions
                                <br>⚖️ Stakes are automatically calculated to maximize profit while minimizing risk
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-header">
                            <div class="setting-icon">⏱️</div>
                            <div class="setting-info">
                                <label for="refreshInterval">Auto-Refresh Interval</label>
                                <span class="setting-description">How often to automatically scan for new opportunities</span>
                            </div>
                        </div>
                        <div class="setting-content">
                            <div class="range-input">
                                <input type="range" id="refreshIntervalRange" min="10" max="300" value="30" class="range-slider">
                                <input type="number" id="refreshInterval" min="10" max="300" value="30" class="range-value">
                                <span class="range-unit">seconds</span>
                            </div>
                            <div class="setting-help">
                                ⚡ Faster intervals use more API quota but catch opportunities quicker
                                <br>🔋 Recommended: 30-60 seconds for optimal balance
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-header">
                            <div class="setting-icon">🎨</div>
                            <div class="setting-info">
                                <label>Interface Theme</label>
                                <span class="setting-description">Choose between light and dark greyscale themes</span>
                            </div>
                        </div>
                        <div class="setting-content">
                            <div class="theme-toggle">
                                <input type="checkbox" id="darkModeToggle" class="theme-checkbox">
                                <label for="darkModeToggle" class="theme-label">
                                    <span class="theme-option">☀️ Light</span>
                                    <span class="theme-slider"></span>
                                    <span class="theme-option">🌙 Dark</span>
                                </label>
                            </div>
                            <div class="setting-help">
                                🌙 Dark theme reduces eye strain during extended trading sessions
                                <br>🎯 Greyscale design minimizes distractions and enhances focus
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveSettings" class="btn btn-primary">
                    <span class="btn-icon">💾</span>
                    <span>Save Settings</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="app.js"></script>
</body>
</html>
