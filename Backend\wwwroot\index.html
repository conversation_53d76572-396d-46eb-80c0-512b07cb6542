<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arb Finder - Sports Betting Arbitrage</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <h1>⚡ Arb Finder</h1>
            <div class="header-controls">
                <button id="refreshBtn" class="btn btn-primary">Find Arbs</button>
                <button id="settingsBtn" class="btn btn-secondary">⚙️ Settings</button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3>Sports</h3>
                    <select id="sportSelect" class="form-control">
                        <option value="soccer_epl">English Premier League</option>
                        <option value="soccer_uefa_champs_league">UEFA Champions League</option>
                        <option value="basketball_nba">NBA</option>
                        <option value="basketball_ncaab">NCAA Basketball</option>
                        <option value="tennis_atp">ATP Tennis</option>
                        <option value="tennis_wta">WTA Tennis</option>
                    </select>
                </div>

                <div class="sidebar-section">
                    <h3>Markets</h3>
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" value="h2h" checked> Head to Head
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="spreads"> Point Spread
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="totals"> Over/Under
                        </label>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Bookmakers</h3>
                    <div id="bookmakersContainer" class="checkbox-group">
                        <div class="loading">Loading bookmakers...</div>
                    </div>
                </div>
            </aside>

            <!-- Results Panel -->
            <main class="results-panel">
                <div class="results-header">
                    <h2>Arbitrage Opportunities</h2>
                    <div class="status-info">
                        <span id="statusText">Ready to find arbitrage opportunities</span>
                        <span id="lastUpdate"></span>
                    </div>
                </div>

                <div id="resultsContainer" class="results-container">
                    <div class="empty-state">
                        <p>Select bookmakers and click "Find Arbs" to discover arbitrage opportunities</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Settings</h3>
                <button id="closeModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="refreshInterval">Refresh Interval (seconds)</label>
                    <input type="number" id="refreshInterval" min="10" max="300" value="30" class="form-control">
                </div>
                <div class="form-group">
                    <label for="apiKeyOverride">API Key Override</label>
                    <input type="password" id="apiKeyOverride" placeholder="Enter your Odds API key" class="form-control">
                    <small>Stored locally in browser</small>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="darkModeToggle"> Dark Mode
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveSettings" class="btn btn-primary">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="app.js"></script>
</body>
</html>
