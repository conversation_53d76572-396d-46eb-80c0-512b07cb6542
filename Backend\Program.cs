using Microsoft.Extensions.Caching.Memory;
using Backend.Services;
using Backend.Controllers;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();
builder.Services.AddScoped<OddsService>();
builder.Services.AddScoped<ArbCalculator>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure pipeline
app.UseCors();
app.UseStaticFiles();

// Map endpoints
app.MapGet("/api/bookmakers", async (OddsService oddsService) =>
{
    try
    {
        var bookmakers = await oddsService.GetBookmakersAsync();
        return Results.Ok(bookmakers);
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error fetching bookmakers: {ex.Message}");
    }
});

app.MapPost("/api/arbs", async (ArbRequest request, OddsService oddsService, ArbCalculator arbCalculator) =>
{
    try
    {
        var odds = await oddsService.GetOddsAsync(request.Sport, request.Markets, request.Bookmakers);
        var opportunities = arbCalculator.FindArbitrageOpportunities(odds);
        return Results.Ok(opportunities);
    }
    catch (HttpRequestException ex) when (ex.Message.Contains("quota"))
    {
        return Results.Problem("API quota exceeded. Please try again later.", statusCode: 429);
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error finding arbitrage opportunities: {ex.Message}");
    }
});

app.Run();

public record ArbRequest(string Sport, string[] Markets, string[] Bookmakers);
