using Microsoft.Extensions.Caching.Memory;
using Backend.Services;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();
builder.Services.AddScoped<OddsService>();
builder.Services.AddScoped<ArbCalculator>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure pipeline
app.UseCors();
app.UseDefaultFiles(); // This will serve index.html by default
app.UseStaticFiles();

// Map endpoints
app.MapGet("/api/bookmakers", async (HttpContext context, OddsService oddsService) =>
{
    try
    {
        var apiKey = context.Request.Headers["X-API-Key"].FirstOrDefault();
        var bookmakers = await oddsService.GetBookmakersAsync(apiKey);
        return Results.Ok(bookmakers);
    }
    catch (InvalidOperationException ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error fetching bookmakers: {ex.Message}");
    }
});

app.MapPost("/api/arbs", async (HttpContext context, ArbRequest request, OddsService oddsService, ArbCalculator arbCalculator) =>
{
    try
    {
        var apiKey = context.Request.Headers["X-API-Key"].FirstOrDefault();
        var odds = await oddsService.GetOddsAsync(request.Sport, request.Markets, request.Bookmakers, apiKey);
        var opportunities = arbCalculator.FindArbitrageOpportunities(odds);
        return Results.Ok(opportunities);
    }
    catch (InvalidOperationException ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
    catch (HttpRequestException ex) when (ex.Message.Contains("quota"))
    {
        return Results.Problem("API quota exceeded. Please try again later.", statusCode: 429);
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error finding arbitrage opportunities: {ex.Message}");
    }
});

app.Run();

public record ArbRequest(string Sport, string[] Markets, string[] Bookmakers);
