using Backend.Models;

namespace Backend.Services;

public class ArbCalculator
{
    public List<ArbOpportunity> FindArbitrageOpportunities(List<OddsResponse> oddsData)
    {
        var opportunities = new List<ArbOpportunity>();

        foreach (var eventData in oddsData)
        {
            foreach (var market in GetUniqueMarkets(eventData))
            {
                var opportunity = CalculateArbitrageForMarket(eventData, market);
                if (opportunity != null)
                {
                    opportunities.Add(opportunity);
                }
            }
        }

        return opportunities.OrderByDescending(o => o.RoiPercentage).ToList();
    }

    private List<string> GetUniqueMarkets(OddsResponse eventData)
    {
        return eventData.Bookmakers
            .SelectMany(b => b.Markets)
            .Select(m => m.Key)
            .Distinct()
            .ToList();
    }

    private ArbOpportunity? CalculateArbitrageForMarket(OddsResponse eventData, string marketKey)
    {
        // Get all outcomes for this market across all bookmakers
        var outcomeOdds = new Dictionary<string, List<OutcomeOdds>>();

        foreach (var bookmaker in eventData.Bookmakers)
        {
            var market = bookmaker.Markets.FirstOrDefault(m => m.Key == marketKey);
            if (market == null) continue;

            foreach (var outcome in market.Outcomes)
            {
                if (!outcomeOdds.ContainsKey(outcome.Name))
                {
                    outcomeOdds[outcome.Name] = new List<OutcomeOdds>();
                }

                outcomeOdds[outcome.Name].Add(new OutcomeOdds
                {
                    Outcome = outcome.Name,
                    Bookmaker = bookmaker.Title,
                    Odds = outcome.Price
                });
            }
        }

        // Need at least 2 outcomes for arbitrage
        if (outcomeOdds.Count < 2) return null;

        // Find best odds for each outcome
        var bestOdds = new List<OutcomeOdds>();
        foreach (var outcomeGroup in outcomeOdds)
        {
            var best = outcomeGroup.Value.OrderByDescending(o => o.Odds).First();
            bestOdds.Add(best);
        }

        // Calculate arbitrage
        var impliedProbabilitySum = bestOdds.Sum(o => 1.0m / o.Odds);
        var roiPercentage = (1.0m / impliedProbabilitySum - 1.0m) * 100;

        // Create stake plans
        var stakePlans = new List<StakePlan>();
        const decimal baseStake = 100m; // Base calculation for percentage

        foreach (var odds in bestOdds)
        {
            var stakePercentage = (1.0m / odds.Odds) / impliedProbabilitySum * 100;
            var stakeAmount = baseStake * stakePercentage / 100;

            stakePlans.Add(new StakePlan
            {
                Outcome = odds.Outcome,
                Bookmaker = odds.Bookmaker,
                Odds = odds.Odds,
                StakePercentage = Math.Round(stakePercentage, 2),
                StakeAmount = Math.Round(stakeAmount, 2)
            });
        }

        return new ArbOpportunity
        {
            EventId = eventData.Id,
            HomeTeam = eventData.HomeTeam,
            AwayTeam = eventData.AwayTeam,
            Market = GetMarketDisplayName(marketKey),
            CommenceTime = eventData.CommenceTime,
            RoiPercentage = Math.Round(roiPercentage, 2),
            StakePlans = stakePlans
        };
    }

    private string GetMarketDisplayName(string marketKey)
    {
        return marketKey switch
        {
            "h2h" => "Head to Head",
            "spreads" => "Point Spread",
            "totals" => "Over/Under",
            _ => marketKey.Replace("_", " ").ToTitleCase()
        };
    }
}

public static class StringExtensions
{
    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        return string.Join(" ", input.Split(' ')
            .Select(word => word.Length == 1
                ? char.ToUpper(word[0]).ToString()
                : char.ToUpper(word[0]) + word.Substring(1).ToLower()));
    }
}
