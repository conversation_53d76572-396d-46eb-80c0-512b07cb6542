namespace Backend.Models;

public class ArbOpportunity
{
    public string EventId { get; set; } = string.Empty;
    public string HomeTeam { get; set; } = string.Empty;
    public string AwayTeam { get; set; } = string.Empty;
    public string Market { get; set; } = string.Empty;
    public DateTime CommenceTime { get; set; }
    public decimal RoiPercentage { get; set; }
    public List<StakePlan> StakePlans { get; set; } = new();
    public bool IsArbitrage => RoiPercentage > 0;
}

public class StakePlan
{
    public string Outcome { get; set; } = string.Empty;
    public string Bookmaker { get; set; } = string.Empty;
    public decimal Odds { get; set; }
    public decimal StakePercentage { get; set; }
    public decimal StakeAmount { get; set; } // Based on $100 total stake
}

public class OutcomeOdds
{
    public string Outcome { get; set; } = string.Empty;
    public string Bookmaker { get; set; } = string.Empty;
    public decimal Odds { get; set; }
}
