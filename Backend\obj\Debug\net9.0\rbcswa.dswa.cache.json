{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["QOQp8wRo+50I9PB3hcS/Uh7NSy1zgplcaQjAX+hCbPo=", "eMxvGq7KPO4gDJ/8Yq/9JMKt7TR7aUvETcM2O/DvIE4=", "AisDZ7+C1fkVUXeeauFIBmJ4p0yqjqFyW8xeJXl7YtE="], "CachedAssets": {"AisDZ7+C1fkVUXeeauFIBmJ4p0yqjqFyW8xeJXl7YtE=": {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-z65ersqd57.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=z65ersqd57}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4v4nixovxs", "Integrity": "oNHeQyAUObKEmhzDn7tVWWip0jro9n86CJI21DYNnUo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "FileLength": 5828, "LastWriteTime": "2025-06-27T12:28:51.7133269+00:00"}, "QOQp8wRo+50I9PB3hcS/Uh7NSy1zgplcaQjAX+hCbPo=": {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-zd1wu8imt9.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=zd1wu8imt9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "69ukul3l90", "Integrity": "KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "FileLength": 6219, "LastWriteTime": "2025-06-27T11:58:41.3223633+00:00"}, "eMxvGq7KPO4gDJ/8Yq/9JMKt7TR7aUvETcM2O/DvIE4=": {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-hogd10aeqz.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=hogd10aeqz}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1gfv02w8g1", "Integrity": "D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "FileLength": 2619, "LastWriteTime": "2025-06-27T12:28:51.7123426+00:00"}}, "CachedCopyCandidates": {}}