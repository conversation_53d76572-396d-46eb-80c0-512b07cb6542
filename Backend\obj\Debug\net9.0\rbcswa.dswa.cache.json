{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["7AXgFFvstbEEvjb/09cnbGuVw9uG2Gn2KZtI5bZVhhk=", "fSD7zxkuuuH+hcE5ILseZuo2r3hRJoV34+HswuT/sAI=", "tYz6wveB1IjczCTZnFUCCJ6NKIawS3XVLhhS2RDIRug="], "CachedAssets": {"7AXgFFvstbEEvjb/09cnbGuVw9uG2Gn2KZtI5bZVhhk=": {"Identity": "C:\\WinSpread\\backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-k5kwazxvlu.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=k5kwazxvlu}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lesrur2pb4", "Integrity": "jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\backend\\wwwroot\\app.js", "FileLength": 4985, "LastWriteTime": "2025-06-27T11:33:33.8372016+00:00"}, "tYz6wveB1IjczCTZnFUCCJ6NKIawS3XVLhhS2RDIRug=": {"Identity": "C:\\WinSpread\\backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-s43tkxfxao.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=s43tkxfxao}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4180wz4ydu", "Integrity": "qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\backend\\wwwroot\\styles.css", "FileLength": 5032, "LastWriteTime": "2025-06-27T11:28:06.3903305+00:00"}, "fSD7zxkuuuH+hcE5ILseZuo2r3hRJoV34+HswuT/sAI=": {"Identity": "C:\\WinSpread\\backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-tfkhlfu4gj.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=tfkhlfu4gj}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3gmhnk4t0p", "Integrity": "Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\backend\\wwwroot\\index.html", "FileLength": 2097, "LastWriteTime": "2025-06-27T11:28:06.3893183+00:00"}}, "CachedCopyCandidates": {}}