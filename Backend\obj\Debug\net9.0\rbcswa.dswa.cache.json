{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["EsSKt6vEhFMfEqm18JvG/pgsEiry5sIWA1qnJHEmdm8=", "3cG7w7iXaBdgtaN+OGVX5ueueVHnVzyT8rTvRErl1Kk=", "NdDtSVA9MkHfOaBcSLtEKDx71BZmOg7RrPOl8vQ1Xzw="], "CachedAssets": {"NdDtSVA9MkHfOaBcSLtEKDx71BZmOg7RrPOl8vQ1Xzw=": {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-icexkurat2.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=icexkurat2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qlw5q5n5s9", "Integrity": "so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "FileLength": 1894, "LastWriteTime": "2025-06-27T11:09:33.9802591+00:00"}, "3cG7w7iXaBdgtaN+OGVX5ueueVHnVzyT8rTvRErl1Kk=": {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-ts83a7p56f.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=ts83a7p56f}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zx53hegtxr", "Integrity": "KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "FileLength": 1387, "LastWriteTime": "2025-06-27T11:09:33.9802591+00:00"}, "EsSKt6vEhFMfEqm18JvG/pgsEiry5sIWA1qnJHEmdm8=": {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-o58y1iq1o7.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=o58y1iq1o7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qi4u0k21dv", "Integrity": "YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "FileLength": 3460, "LastWriteTime": "2025-06-27T11:09:33.9802591+00:00"}}, "CachedCopyCandidates": {}}