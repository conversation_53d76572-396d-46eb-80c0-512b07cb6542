// App State
let bookmakers = [];
let refreshInterval = 30; // seconds
let refreshTimer = null;
let isLoading = false;

// DOM Elements
const elements = {
    sportSelect: document.getElementById('sportSelect'),
    bookmakersContainer: document.getElementById('bookmakersContainer'),
    resultsContainer: document.getElementById('resultsContainer'),
    refreshBtn: document.getElementById('refreshBtn'),
    settingsBtn: document.getElementById('settingsBtn'),
    settingsModal: document.getElementById('settingsModal'),
    closeModal: document.getElementById('closeModal'),
    saveSettings: document.getElementById('saveSettings'),
    refreshIntervalInput: document.getElementById('refreshInterval'),
    apiKeyOverrideInput: document.getElementById('apiKeyOverride'),
    darkModeToggle: document.getElementById('darkModeToggle'),
    statusText: document.getElementById('statusText'),
    lastUpdate: document.getElementById('lastUpdate'),
    toastContainer: document.getElementById('toastContainer')
};

// Initialize app
document.addEventListener('DOMContentLoaded', async () => {
    loadSettings();
    await loadBookmakers();
    setupEventListeners();
    startAutoRefresh();
});

// Load settings from localStorage
function loadSettings() {
    const savedInterval = localStorage.getItem('refreshInterval');
    if (savedInterval) {
        refreshInterval = parseInt(savedInterval);
        elements.refreshIntervalInput.value = refreshInterval;
    }

    const savedApiKey = localStorage.getItem('apiKeyOverride');
    if (savedApiKey) {
        elements.apiKeyOverrideInput.value = savedApiKey;
    }

    const darkMode = localStorage.getItem('darkMode') === 'true';
    elements.darkModeToggle.checked = darkMode;
    if (darkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    }
}

// Load bookmakers from API
async function loadBookmakers() {
    try {
        showStatus('Loading bookmakers...');
        const response = await fetch('/api/bookmakers');
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        bookmakers = await response.json();
        renderBookmakers();
        showStatus('Ready to find arbitrage opportunities');
    } catch (error) {
        console.error('Error loading bookmakers:', error);
        showToast('Failed to load bookmakers: ' + error.message, 'error');
        elements.bookmakersContainer.innerHTML = '<div class="error">Failed to load bookmakers</div>';
    }
}

// Render bookmakers checkboxes
function renderBookmakers() {
    if (bookmakers.length === 0) {
        elements.bookmakersContainer.innerHTML = '<div class="empty-state">No bookmakers available</div>';
        return;
    }

    const html = bookmakers.map(bookmaker => `
        <label class="checkbox-label">
            <input type="checkbox" value="${bookmaker.key}" data-title="${bookmaker.title}">
            ${bookmaker.title}
        </label>
    `).join('');

    elements.bookmakersContainer.innerHTML = html;

    // Select first few bookmakers by default
    const checkboxes = elements.bookmakersContainer.querySelectorAll('input[type="checkbox"]');
    for (let i = 0; i < Math.min(5, checkboxes.length); i++) {
        checkboxes[i].checked = true;
    }
}

// Setup event listeners
function setupEventListeners() {
    elements.refreshBtn.addEventListener('click', findArbitrageOpportunities);
    elements.settingsBtn.addEventListener('click', () => elements.settingsModal.style.display = 'block');
    elements.closeModal.addEventListener('click', () => elements.settingsModal.style.display = 'none');
    elements.saveSettings.addEventListener('click', saveSettings);

    // Close modal when clicking outside
    elements.settingsModal.addEventListener('click', (e) => {
        if (e.target === elements.settingsModal) {
            elements.settingsModal.style.display = 'none';
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && elements.settingsModal.style.display === 'block') {
            elements.settingsModal.style.display = 'none';
        }
    });
}

// Save settings
function saveSettings() {
    const newInterval = parseInt(elements.refreshIntervalInput.value);
    const apiKey = elements.apiKeyOverrideInput.value.trim();
    const darkMode = elements.darkModeToggle.checked;

    // Validate interval
    if (newInterval < 10 || newInterval > 300) {
        showToast('Refresh interval must be between 10 and 300 seconds', 'error');
        return;
    }

    // Save to localStorage
    localStorage.setItem('refreshInterval', newInterval.toString());
    localStorage.setItem('apiKeyOverride', apiKey);
    localStorage.setItem('darkMode', darkMode.toString());

    // Apply settings
    refreshInterval = newInterval;
    
    if (darkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.removeAttribute('data-theme');
    }

    // Restart auto-refresh with new interval
    startAutoRefresh();

    elements.settingsModal.style.display = 'none';
    showToast('Settings saved successfully', 'success');
}

// Start auto-refresh timer
function startAutoRefresh() {
    if (refreshTimer) {
        clearInterval(refreshTimer);
    }

    refreshTimer = setInterval(() => {
        if (!isLoading) {
            findArbitrageOpportunities();
        }
    }, refreshInterval * 1000);
}

// Get selected values from form
function getSelectedValues() {
    const sport = elements.sportSelect.value;
    
    const marketCheckboxes = document.querySelectorAll('.sidebar-section:nth-child(2) input[type="checkbox"]:checked');
    const markets = Array.from(marketCheckboxes).map(cb => cb.value);
    
    const bookmakersCheckboxes = elements.bookmakersContainer.querySelectorAll('input[type="checkbox"]:checked');
    const selectedBookmakers = Array.from(bookmakersCheckboxes).map(cb => cb.value);

    return { sport, markets, bookmakers: selectedBookmakers };
}

// Find arbitrage opportunities
async function findArbitrageOpportunities() {
    if (isLoading) return;

    const { sport, markets, bookmakers: selectedBookmakers } = getSelectedValues();

    if (markets.length === 0) {
        showToast('Please select at least one market', 'warning');
        return;
    }

    if (selectedBookmakers.length < 2) {
        showToast('Please select at least 2 bookmakers', 'warning');
        return;
    }

    try {
        isLoading = true;
        elements.refreshBtn.disabled = true;
        elements.refreshBtn.textContent = 'Finding...';
        showStatus('Searching for arbitrage opportunities...');

        const requestBody = {
            sport,
            markets,
            bookmakers: selectedBookmakers
        };

        const response = await fetch('/api/arbs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (response.status === 429) {
            throw new Error('API quota exceeded. Please try again later.');
        }

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const opportunities = await response.json();
        renderResults(opportunities);
        
        const arbCount = opportunities.filter(o => o.isArbitrage).length;
        showStatus(`Found ${arbCount} arbitrage opportunities out of ${opportunities.length} events`);
        elements.lastUpdate.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

    } catch (error) {
        console.error('Error finding arbitrage opportunities:', error);
        showToast(error.message, 'error');
        showStatus('Error occurred while searching');
    } finally {
        isLoading = false;
        elements.refreshBtn.disabled = false;
        elements.refreshBtn.textContent = 'Find Arbs';
    }
}

// Render results table
function renderResults(opportunities) {
    if (opportunities.length === 0) {
        elements.resultsContainer.innerHTML = `
            <div class="empty-state">
                <p>No events found for the selected criteria</p>
            </div>
        `;
        return;
    }

    const tableHtml = `
        <table class="results-table">
            <thead>
                <tr>
                    <th>Event</th>
                    <th>Market</th>
                    <th>Start Time</th>
                    <th>ROI %</th>
                    <th>Stake Plan</th>
                </tr>
            </thead>
            <tbody>
                ${opportunities.map(opp => renderOpportunityRow(opp)).join('')}
            </tbody>
        </table>
    `;

    elements.resultsContainer.innerHTML = tableHtml;
}

// Render individual opportunity row
function renderOpportunityRow(opportunity) {
    const rowClass = opportunity.isArbitrage ? 'arbitrage' : '';
    const roiClass = opportunity.roiPercentage > 0 ? 'roi-positive' : 'roi-neutral';
    const startTime = new Date(opportunity.commenceTime).toLocaleString();

    const stakePlanHtml = opportunity.stakePlans.map(plan => `
        <div class="stake-plan-item">
            <strong>${plan.outcome}</strong> @ ${plan.odds}
            (${plan.bookmaker}): $${plan.stakeAmount} (${plan.stakePercentage}%)
        </div>
    `).join('');

    return `
        <tr class="${rowClass}">
            <td>
                <div><strong>${opportunity.homeTeam} vs ${opportunity.awayTeam}</strong></div>
            </td>
            <td>${opportunity.market}</td>
            <td>${startTime}</td>
            <td class="${roiClass}">
                ${opportunity.roiPercentage > 0 ? '+' : ''}${opportunity.roiPercentage}%
            </td>
            <td>
                <div class="stake-plan">
                    ${stakePlanHtml}
                </div>
            </td>
        </tr>
    `;
}

// Show status message
function showStatus(message) {
    elements.statusText.textContent = message;
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    elements.toastContainer.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);

    // Allow manual close by clicking
    toast.addEventListener('click', () => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (refreshTimer) {
        clearInterval(refreshTimer);
    }
});

// Future enhancement: WebSocket connection for real-time updates
// function connectWebSocket() {
//     const ws = new WebSocket('ws://localhost:5000/ws');
//     ws.onmessage = (event) => {
//         const data = JSON.parse(event.data);
//         if (data.type === 'arbitrage-update') {
//             renderResults(data.opportunities);
//         }
//     };
// }
