// App State
let sports = [];
let markets = [];
let bookmakers = [];
let selectedSports = [];
let selectedMarkets = [];
let selectedBookmakers = [];
let refreshInterval = 30; // seconds
let refreshTimer = null;
let isLoading = false;

// DOM Elements
const elements = {
    sportsContainer: document.getElementById('sportsContainer'),
    marketsContainer: document.getElementById('marketsContainer'),
    bookmakersContainer: document.getElementById('bookmakersContainer'),
    resultsContainer: document.getElementById('resultsContainer'),
    refreshBtn: document.getElementById('refreshBtn'),
    settingsBtn: document.getElementById('settingsBtn'),
    settingsModal: document.getElementById('settingsModal'),
    closeModal: document.getElementById('closeModal'),
    saveSettings: document.getElementById('saveSettings'),
    refreshIntervalInput: document.getElementById('refreshInterval'),
    refreshIntervalRange: document.getElementById('refreshIntervalRange'),
    apiKeyOverrideInput: document.getElementById('apiKeyOverride'),
    darkModeToggle: document.getElementById('darkModeToggle'),
    statusText: document.getElementById('statusText'),
    statusDot: document.getElementById('statusDot'),
    lastUpdate: document.getElementById('lastUpdate'),
    toastContainer: document.getElementById('toastContainer'),
    sportsBadge: document.getElementById('sportsBadge'),
    marketsBadge: document.getElementById('marketsBadge'),
    bookmakersBadge: document.getElementById('bookmakersBadge'),
    selectAllMarkets: document.getElementById('selectAllMarkets'),
    selectAllBookmakers: document.getElementById('selectAllBookmakers'),
    totalOpportunities: document.getElementById('totalOpportunities'),
    profitableArbs: document.getElementById('profitableArbs'),
    bestRoi: document.getElementById('bestRoi')
};

// Initialize app
document.addEventListener('DOMContentLoaded', async () => {
    loadSettings();
    await loadSports();
    await loadMarkets();
    await loadBookmakers();
    setupEventListeners();
    updateBadges();
    startAutoRefresh();
});

// Load settings from localStorage
function loadSettings() {
    const savedInterval = localStorage.getItem('refreshInterval');
    if (savedInterval) {
        refreshInterval = parseInt(savedInterval);
        elements.refreshIntervalInput.value = refreshInterval;
        elements.refreshIntervalRange.value = refreshInterval;
    }

    const savedApiKey = localStorage.getItem('apiKeyOverride');
    if (savedApiKey) {
        elements.apiKeyOverrideInput.value = savedApiKey;
    }

    const darkMode = localStorage.getItem('darkMode') === 'true';
    elements.darkModeToggle.checked = darkMode;
    if (darkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    }

    // Load selections
    const savedSports = JSON.parse(localStorage.getItem('selectedSports') || '[]');
    const savedMarkets = JSON.parse(localStorage.getItem('selectedMarkets') || '["h2h"]');
    const savedBookmakers = JSON.parse(localStorage.getItem('selectedBookmakers') || '[]');

    selectedSports = savedSports;
    selectedMarkets = savedMarkets;
    selectedBookmakers = savedBookmakers;
}

// Get API key from localStorage
function getApiKey() {
    const apiKey = localStorage.getItem('apiKeyOverride');
    return apiKey && apiKey.trim() !== '' ? apiKey.trim() : null;
}

// Load sports from API
async function loadSports() {
    try {
        const response = await fetch('/api/sports');
        if (!response.ok) throw new Error('Failed to load sports');

        sports = await response.json();
        renderSports();
    } catch (error) {
        console.error('Error loading sports:', error);
        showToast('Failed to load sports: ' + error.message, 'error');
    }
}

// Load markets from API
async function loadMarkets() {
    try {
        const response = await fetch('/api/markets');
        if (!response.ok) throw new Error('Failed to load markets');

        markets = await response.json();
        renderMarkets();
    } catch (error) {
        console.error('Error loading markets:', error);
        showToast('Failed to load markets: ' + error.message, 'error');
    }
}

// Render sports grid
function renderSports() {
    if (sports.length === 0) {
        elements.sportsContainer.innerHTML = '<div class="error">No sports available</div>';
        return;
    }

    // Group sports by category
    const sportsByCategory = sports.reduce((acc, sport) => {
        if (!acc[sport.category]) acc[sport.category] = [];
        acc[sport.category].push(sport);
        return acc;
    }, {});

    let html = '';
    Object.entries(sportsByCategory).forEach(([category, categorySports]) => {
        html += `<div class="category-section">
            <div class="category-title">${category}</div>`;

        categorySports.forEach(sport => {
            const isSelected = selectedSports.includes(sport.key);
            html += `
                <div class="sport-card ${isSelected ? 'selected' : ''}" data-sport="${sport.key}">
                    <div class="sport-icon">${sport.icon}</div>
                    <div class="sport-info">
                        <div class="sport-title">${sport.title}</div>
                        <div class="sport-category">${sport.category}</div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
    });

    elements.sportsContainer.innerHTML = html;

    // Add click handlers
    elements.sportsContainer.querySelectorAll('.sport-card').forEach(card => {
        card.addEventListener('click', () => toggleSportSelection(card.dataset.sport));
    });

    // Auto-select first sport if none selected
    if (selectedSports.length === 0 && sports.length > 0) {
        selectedSports = [sports[0].key];
        localStorage.setItem('selectedSports', JSON.stringify(selectedSports));
        renderSports();
        updateBadges();
    }
}

// Render markets grid
function renderMarkets() {
    if (markets.length === 0) {
        elements.marketsContainer.innerHTML = '<div class="error">No markets available</div>';
        return;
    }

    const html = markets.map(market => {
        const isSelected = selectedMarkets.includes(market.key);
        return `
            <div class="selection-card ${isSelected ? 'selected' : ''}" data-market="${market.key}">
                <div class="card-icon">${market.icon}</div>
                <div class="card-title">${market.title}</div>
                <div class="card-description">${market.description}</div>
            </div>
        `;
    }).join('');

    elements.marketsContainer.innerHTML = html;

    // Add click handlers
    elements.marketsContainer.querySelectorAll('.selection-card').forEach(card => {
        card.addEventListener('click', () => toggleMarketSelection(card.dataset.market));
    });
}

// Load bookmakers from API
async function loadBookmakers() {
    const apiKey = getApiKey();

    if (!apiKey) {
        elements.bookmakersContainer.innerHTML = `
            <div class="api-key-prompt">
                <p>⚠️ API Key Required</p>
                <p>Please configure your Odds API key in Settings to load bookmakers.</p>
                <button onclick="document.getElementById('settingsModal').style.display='block'" class="btn btn-primary">
                    Open Settings
                </button>
            </div>
        `;
        showStatus('API key required - please configure in Settings');
        return;
    }

    try {
        showStatus('Loading bookmakers...');
        const response = await fetch('/api/bookmakers', {
            headers: {
                'X-API-Key': apiKey
            }
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        bookmakers = await response.json();
        renderBookmakers();
        showStatus('Ready to find arbitrage opportunities');
    } catch (error) {
        console.error('Error loading bookmakers:', error);
        showToast('Failed to load bookmakers: ' + error.message, 'error');
        elements.bookmakersContainer.innerHTML = `
            <div class="error">
                <p>Failed to load bookmakers</p>
                <p>${error.message}</p>
                <button onclick="loadBookmakers()" class="btn btn-secondary">Retry</button>
            </div>
        `;
    }
}

// Render bookmakers grid
function renderBookmakers() {
    if (bookmakers.length === 0) {
        elements.bookmakersContainer.innerHTML = '<div class="error">No bookmakers available</div>';
        return;
    }

    const html = bookmakers.map(bookmaker => {
        const isSelected = selectedBookmakers.includes(bookmaker.key);
        return `
            <div class="selection-card ${isSelected ? 'selected' : ''}" data-bookmaker="${bookmaker.key}">
                <div class="card-icon">🏪</div>
                <div class="card-title">${bookmaker.title}</div>
            </div>
        `;
    }).join('');

    elements.bookmakersContainer.innerHTML = html;

    // Add click handlers
    elements.bookmakersContainer.querySelectorAll('.selection-card').forEach(card => {
        card.addEventListener('click', () => toggleBookmakerSelection(card.dataset.bookmaker));
    });

    // Auto-select first few bookmakers if none selected
    if (selectedBookmakers.length === 0 && bookmakers.length > 0) {
        selectedBookmakers = bookmakers.slice(0, Math.min(5, bookmakers.length)).map(b => b.key);
        localStorage.setItem('selectedBookmakers', JSON.stringify(selectedBookmakers));
        renderBookmakers();
        updateBadges();
    }
}

// Toggle sport selection
function toggleSportSelection(sportKey) {
    const index = selectedSports.indexOf(sportKey);
    if (index > -1) {
        selectedSports.splice(index, 1);
    } else {
        selectedSports.push(sportKey);
    }

    localStorage.setItem('selectedSports', JSON.stringify(selectedSports));
    renderSports();
    updateBadges();
}

// Toggle market selection
function toggleMarketSelection(marketKey) {
    const index = selectedMarkets.indexOf(marketKey);
    if (index > -1) {
        selectedMarkets.splice(index, 1);
    } else {
        selectedMarkets.push(marketKey);
    }

    localStorage.setItem('selectedMarkets', JSON.stringify(selectedMarkets));
    renderMarkets();
    updateBadges();
}

// Toggle bookmaker selection
function toggleBookmakerSelection(bookmakerKey) {
    const index = selectedBookmakers.indexOf(bookmakerKey);
    if (index > -1) {
        selectedBookmakers.splice(index, 1);
    } else {
        selectedBookmakers.push(bookmakerKey);
    }

    localStorage.setItem('selectedBookmakers', JSON.stringify(selectedBookmakers));
    renderBookmakers();
    updateBadges();
}

// Update selection badges
function updateBadges() {
    elements.sportsBadge.textContent = `${selectedSports.length} selected`;
    elements.marketsBadge.textContent = `${selectedMarkets.length} selected`;
    elements.bookmakersBadge.textContent = `${selectedBookmakers.length} selected`;

    console.log('Updated badges:', {
        sports: selectedSports,
        markets: selectedMarkets,
        bookmakers: selectedBookmakers
    });
}

// Select all markets
function selectAllMarkets() {
    selectedMarkets = markets.map(m => m.key);
    localStorage.setItem('selectedMarkets', JSON.stringify(selectedMarkets));
    renderMarkets();
    updateBadges();
}

// Select all bookmakers
function selectAllBookmakers() {
    selectedBookmakers = bookmakers.map(b => b.key);
    localStorage.setItem('selectedBookmakers', JSON.stringify(selectedBookmakers));
    renderBookmakers();
    updateBadges();
}

// Setup event listeners
function setupEventListeners() {
    elements.refreshBtn.addEventListener('click', findArbitrageOpportunities);
    elements.settingsBtn.addEventListener('click', () => elements.settingsModal.style.display = 'block');
    elements.closeModal.addEventListener('click', () => elements.settingsModal.style.display = 'none');
    elements.saveSettings.addEventListener('click', saveSettings);
    elements.selectAllMarkets.addEventListener('click', selectAllMarkets);
    elements.selectAllBookmakers.addEventListener('click', selectAllBookmakers);

    // Range slider sync
    elements.refreshIntervalRange.addEventListener('input', (e) => {
        elements.refreshIntervalInput.value = e.target.value;
    });

    elements.refreshIntervalInput.addEventListener('input', (e) => {
        elements.refreshIntervalRange.value = e.target.value;
    });

    // Close modal when clicking backdrop
    elements.settingsModal.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal-backdrop') || e.target === elements.settingsModal) {
            elements.settingsModal.style.display = 'none';
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && elements.settingsModal.style.display === 'block') {
            elements.settingsModal.style.display = 'none';
        }
    });
}

// Save settings
function saveSettings() {
    const newInterval = parseInt(elements.refreshIntervalInput.value);
    const apiKey = elements.apiKeyOverrideInput.value.trim();
    const darkMode = elements.darkModeToggle.checked;

    // Validate interval
    if (newInterval < 10 || newInterval > 300) {
        showToast('Refresh interval must be between 10 and 300 seconds', 'error');
        return;
    }

    // Validate API key
    if (!apiKey) {
        showToast('Please enter your Odds API key', 'error');
        return;
    }

    const previousApiKey = localStorage.getItem('apiKeyOverride');

    // Save to localStorage
    localStorage.setItem('refreshInterval', newInterval.toString());
    localStorage.setItem('apiKeyOverride', apiKey);
    localStorage.setItem('darkMode', darkMode.toString());

    // Apply settings
    refreshInterval = newInterval;

    if (darkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.removeAttribute('data-theme');
    }

    // Restart auto-refresh with new interval
    startAutoRefresh();

    elements.settingsModal.style.display = 'none';
    showToast('Settings saved successfully', 'success');

    // Reload bookmakers if API key changed
    if (apiKey !== previousApiKey) {
        loadBookmakers();
    }
}

// Start auto-refresh timer
function startAutoRefresh() {
    if (refreshTimer) {
        clearInterval(refreshTimer);
    }

    refreshTimer = setInterval(() => {
        if (!isLoading) {
            findArbitrageOpportunities();
        }
    }, refreshInterval * 1000);
}

// Get selected values
function getSelectedValues() {
    return {
        sports: selectedSports,
        markets: selectedMarkets,
        bookmakers: selectedBookmakers
    };
}

// Find arbitrage opportunities
async function findArbitrageOpportunities() {
    if (isLoading) return;

    const apiKey = getApiKey();
    console.log('API Key check:', apiKey ? 'Present' : 'Missing');

    if (!apiKey) {
        showToast('Please configure your API key in Settings first', 'warning');
        elements.settingsModal.style.display = 'block';
        return;
    }

    const { sports, markets, bookmakers } = getSelectedValues();

    console.log('Selected values:', { sports, markets, bookmakers });

    if (sports.length === 0) {
        showToast('Please select at least one sport', 'warning');
        return;
    }

    if (markets.length === 0) {
        showToast('Please select at least one market', 'warning');
        return;
    }

    if (bookmakers.length < 2) {
        showToast('Please select at least 2 bookmakers', 'warning');
        return;
    }

    try {
        isLoading = true;
        elements.refreshBtn.disabled = true;
        elements.refreshBtn.innerHTML = '<span class="btn-icon">⏳</span><span>Searching...</span>';
        showStatus('Searching for arbitrage opportunities...', 'active');

        // Process each sport separately
        let allOpportunities = [];

        for (const sport of sports) {
            const requestBody = {
                sport,
                markets,
                bookmakers
            };

            const response = await fetch('/api/arbs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': apiKey
                },
                body: JSON.stringify(requestBody)
            });

            if (response.status === 429) {
                throw new Error('API quota exceeded. Please try again later.');
            }

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: response.statusText }));
                throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            const opportunities = await response.json();
            allOpportunities = allOpportunities.concat(opportunities);
        }

        renderResults(allOpportunities);
        updateDashboardStats(allOpportunities);

        const arbCount = allOpportunities.filter(o => o.isArbitrage).length;
        showStatus(`Found ${arbCount} arbitrage opportunities out of ${allOpportunities.length} events`, 'success');
        elements.lastUpdate.textContent = new Date().toLocaleTimeString();

    } catch (error) {
        console.error('Error finding arbitrage opportunities:', error);
        console.error('Selected values:', { sports, markets, bookmakers });
        showToast(`Error: ${error.message}`, 'error');
        showStatus('Error occurred while searching', 'error');
    } finally {
        isLoading = false;
        elements.refreshBtn.disabled = false;
        elements.refreshBtn.innerHTML = '<span class="btn-icon">🔍</span><span>Find Opportunities</span>';
    }
}

// Render results table
function renderResults(opportunities) {
    if (opportunities.length === 0) {
        elements.resultsContainer.innerHTML = `
            <div class="empty-state">
                <p>No events found for the selected criteria</p>
            </div>
        `;
        return;
    }

    const tableHtml = `
        <table class="results-table">
            <thead>
                <tr>
                    <th>Event</th>
                    <th>Market</th>
                    <th>Start Time</th>
                    <th>ROI %</th>
                    <th>Stake Plan</th>
                </tr>
            </thead>
            <tbody>
                ${opportunities.map(opp => renderOpportunityRow(opp)).join('')}
            </tbody>
        </table>
    `;

    elements.resultsContainer.innerHTML = tableHtml;
}

// Render individual opportunity row
function renderOpportunityRow(opportunity) {
    const rowClass = opportunity.isArbitrage ? 'arbitrage' : '';
    const roiClass = opportunity.roiPercentage > 0 ? 'roi-positive' : 'roi-neutral';
    const startTime = new Date(opportunity.commenceTime).toLocaleString();

    const stakePlanHtml = opportunity.stakePlans.map(plan => `
        <div class="stake-plan-item">
            <strong>${plan.outcome}</strong> @ ${plan.odds}
            (${plan.bookmaker}): $${plan.stakeAmount} (${plan.stakePercentage}%)
        </div>
    `).join('');

    return `
        <tr class="${rowClass}">
            <td>
                <div><strong>${opportunity.homeTeam} vs ${opportunity.awayTeam}</strong></div>
            </td>
            <td>${opportunity.market}</td>
            <td>${startTime}</td>
            <td class="${roiClass}">
                ${opportunity.roiPercentage > 0 ? '+' : ''}${opportunity.roiPercentage}%
            </td>
            <td>
                <div class="stake-plan">
                    ${stakePlanHtml}
                </div>
            </td>
        </tr>
    `;
}

// Show status message
function showStatus(message, type = 'default') {
    elements.statusText.textContent = message;

    // Update status dot
    elements.statusDot.className = 'status-dot';
    if (type === 'active') {
        elements.statusDot.classList.add('active');
    } else if (type === 'error') {
        elements.statusDot.classList.add('error');
    }
}

// Update dashboard statistics
function updateDashboardStats(opportunities) {
    const profitable = opportunities.filter(o => o.isArbitrage);
    const bestRoi = profitable.length > 0 ? Math.max(...profitable.map(o => o.roiPercentage)) : 0;

    elements.totalOpportunities.textContent = opportunities.length;
    elements.profitableArbs.textContent = profitable.length;
    elements.bestRoi.textContent = `${bestRoi.toFixed(2)}%`;
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    elements.toastContainer.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);

    // Allow manual close by clicking
    toast.addEventListener('click', () => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (refreshTimer) {
        clearInterval(refreshTimer);
    }
});

// Future enhancement: WebSocket connection for real-time updates
// function connectWebSocket() {
//     const ws = new WebSocket('ws://localhost:5000/ws');
//     ws.onmessage = (event) => {
//         const data = JSON.parse(event.data);
//         if (data.type === 'arbitrage-update') {
//             renderResults(data.opportunities);
//         }
//     };
// }
