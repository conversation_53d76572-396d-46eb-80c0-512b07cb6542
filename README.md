# Sports Betting Arbitrage Finder

A comprehensive web application for finding sports betting arbitrage opportunities using The Odds API. Built with C# 13/.NET 8 backend and vanilla JavaScript frontend.

## Features

- **Real-time Arbitrage Detection**: Automatically finds profitable arbitrage opportunities across multiple bookmakers
- **Multiple Sports Support**: Soccer, Basketball, Tennis (easily extensible)
- **Configurable Markets**: Head-to-Head, Point Spreads, Over/Under totals
- **Smart Caching**: Reduces API calls with intelligent caching strategy
- **Auto-refresh**: Configurable refresh intervals (10-300 seconds)
- **ROI Calculation**: Shows exact return on investment percentages
- **Stake Planning**: Calculates optimal stake distribution across bookmakers
- **Dark/Light Theme**: User-configurable UI themes
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Graceful handling of API quota limits and network issues

## Architecture

### Backend (C# 13/.NET 8)
- **Minimal API**: Clean, modern API endpoints
- **Services Layer**: Modular business logic separation
- **Memory Caching**: Efficient data caching with configurable TTL
- **HTTP Client Factory**: Robust HTTP client management
- **Polly Integration**: Retry policies for transient failures

### Frontend (Vanilla JavaScript)
- **No Framework Dependencies**: Pure HTML/CSS/JS for maximum performance
- **Modern ES6+**: Clean, maintainable JavaScript code
- **CSS Grid/Flexbox**: Responsive layout system
- **Local Storage**: Persistent user settings
- **Toast Notifications**: User-friendly error/success messages

## Project Structure

```
Backend/
├── Program.cs              # Application bootstrap and API endpoints
├── Models/
│   ├── OddsResponse.cs     # API response models
│   └── ArbOpportunity.cs   # Arbitrage calculation models
├── Services/
│   ├── OddsService.cs      # The Odds API integration
│   └── ArbCalculator.cs    # Arbitrage detection logic
├── appsettings.json        # Configuration settings
└── Backend.csproj          # Project file

wwwroot/
├── index.html              # Main application page
├── styles.css              # Responsive CSS with theming
└── app.js                  # Frontend application logic

README.md                   # This file
```

## Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [The Odds API Key](https://the-odds-api.com/) (free tier available)

## Setup & Installation

### 1. Clone/Download the Project
```bash
# If using git
git clone <repository-url>
cd sports-arbitrage-finder

# Or extract the provided files to a directory
```

### 2. Get Your API Key
1. Visit [The Odds API](https://the-odds-api.com/)
2. Sign up for a free account
3. Copy your API key from the dashboard

### 3. Configure API Key
Choose one of these methods:

**Method A: Environment Variable (Recommended)**
```bash
# Windows (Command Prompt)
set ODDS_API_KEY=your_api_key_here

# Windows (PowerShell)
$env:ODDS_API_KEY="your_api_key_here"

# macOS/Linux
export ODDS_API_KEY=your_api_key_here
```

**Method B: Configuration File**
Edit `Backend/appsettings.json`:
```json
{
  "OddsApi": {
    "ApiKey": "your_api_key_here"
  }
}
```

**Method C: Runtime Override**
Enter your API key in the Settings modal within the application.

### 4. Build & Run

```bash
# Navigate to the Backend directory
cd Backend

# Restore dependencies
dotnet restore

# Run the application
dotnet run
```

The application will start on `http://localhost:5000`

## Usage

### 1. Access the Application
Open your browser and navigate to `http://localhost:5000`

### 2. Configure Settings
- Click the ⚙️ Settings button
- Set your API key if not configured via environment/config
- Adjust refresh interval (default: 30 seconds)
- Toggle dark mode if desired

### 3. Select Criteria
- **Sport**: Choose from available sports (EPL, Champions League, NBA, etc.)
- **Markets**: Select betting markets (Head-to-Head, Spreads, Totals)
- **Bookmakers**: Choose at least 2 bookmakers to compare

### 4. Find Arbitrage Opportunities
- Click "Find Arbs" or wait for auto-refresh
- Green rows indicate profitable arbitrage opportunities
- ROI% shows the guaranteed profit percentage
- Stake Plan shows optimal bet distribution

### 5. Interpret Results
- **Positive ROI**: Guaranteed profit opportunity
- **Zero/Negative ROI**: No arbitrage available
- **Stake Plan**: Shows how much to bet on each outcome

## API Endpoints

### GET /api/bookmakers
Returns list of available bookmakers.

**Response:**
```json
[
  {
    "key": "draftkings",
    "title": "DraftKings"
  }
]
```

### POST /api/arbs
Finds arbitrage opportunities for given criteria.

**Request:**
```json
{
  "sport": "soccer_epl",
  "markets": ["h2h"],
  "bookmakers": ["draftkings", "fanduel"]
}
```

**Response:**
```json
[
  {
    "eventId": "abc123",
    "homeTeam": "Arsenal",
    "awayTeam": "Chelsea",
    "market": "Head to Head",
    "commenceTime": "2024-01-15T15:00:00Z",
    "roiPercentage": 2.5,
    "isArbitrage": true,
    "stakePlans": [
      {
        "outcome": "Arsenal",
        "bookmaker": "DraftKings",
        "odds": 2.1,
        "stakePercentage": 48.78,
        "stakeAmount": 48.78
      }
    ]
  }
]
```

## Configuration

### Supported Sports
- `soccer_epl` - English Premier League
- `soccer_uefa_champs_league` - UEFA Champions League
- `basketball_nba` - NBA
- `basketball_ncaab` - NCAA Basketball
- `tennis_atp` - ATP Tennis
- `tennis_wta` - WTA Tennis

### Supported Markets
- `h2h` - Head to Head (Winner)
- `spreads` - Point Spread
- `totals` - Over/Under Totals

### Cache Settings
- Bookmakers: 30 minutes
- Odds Data: 25 seconds (slightly less than refresh interval)

## Troubleshooting

### Common Issues

**"API quota exceeded" Error**
- The Odds API has usage limits
- Free tier: 500 requests/month
- Wait for quota reset or upgrade plan
- Reduce refresh frequency in settings

**"Failed to load bookmakers" Error**
- Check your API key configuration
- Verify internet connection
- Check API service status

**No Arbitrage Opportunities Found**
- Arbitrage opportunities are rare
- Try different sports/markets
- Ensure multiple bookmakers are selected
- Market conditions may not favor arbitrage

**Application Won't Start**
- Ensure .NET 8 SDK is installed
- Check port 5000 isn't in use
- Verify all files are present

### Debug Mode
Run with detailed logging:
```bash
dotnet run --environment Development
```

## Performance Considerations

- **API Rate Limits**: Free tier allows ~16 requests/hour with 30s refresh
- **Caching Strategy**: Reduces API calls by 85%+
- **Memory Usage**: Minimal footprint with efficient caching
- **Network**: Optimized requests with selective bookmaker filtering

## Future Enhancements

The following features are documented for future development:

### Planned Features
- **WebSocket Integration**: Real-time updates without polling
- **User Authentication**: Personal dashboards and preferences
- **Historical Analysis**: Back-testing and trend analysis
- **Advanced Filtering**: Minimum ROI, time-based filters
- **Notification System**: Email/SMS alerts for opportunities
- **Mobile App**: Native iOS/Android applications
- **Docker Support**: Containerized deployment
- **Swagger Documentation**: Interactive API documentation

### Technical Improvements
- **Database Integration**: Persistent data storage
- **Microservices**: Scalable architecture
- **Load Balancing**: High-availability deployment
- **Monitoring**: Application performance monitoring
- **Testing Suite**: Comprehensive unit/integration tests

## License

This project is provided as-is for educational and personal use. Please ensure compliance with The Odds API terms of service and local gambling regulations.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify your API key and configuration
3. Review browser console for JavaScript errors
4. Check application logs for backend errors

## API Credits

This application uses [The Odds API](https://the-odds-api.com/) for sports betting odds data. Please respect their terms of service and rate limits.
