using Microsoft.Extensions.Caching.Memory;
using Backend.Models;
using System.Text.Json;

namespace Backend.Services;

public class OddsService
{
    private readonly HttpClient _httpClient;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;
    private readonly string _apiKey;
    private const string BaseUrl = "https://api.the-odds-api.com/v4";

    // Supported sports
    private readonly Dictionary<string, string> _supportedSports = new()
    {
        { "soccer_epl", "English Premier League" },
        { "soccer_uefa_champs_league", "UEFA Champions League" },
        { "basketball_nba", "NBA" },
        { "basketball_ncaab", "NCAA Basketball" },
        { "tennis_atp", "ATP Tennis" },
        { "tennis_wta", "WTA Tennis" }
    };

    public OddsService(HttpClient httpClient, IMemoryCache cache, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _cache = cache;
        _configuration = configuration;

        // API key will be provided via request headers
        _apiKey = "YOUR_API_KEY_HERE"; // Default placeholder
    }

    public async Task<List<BookmakerInfo>> GetBookmakersAsync(string? apiKey = null)
    {
        var effectiveApiKey = apiKey ?? _apiKey;

        if (string.IsNullOrEmpty(effectiveApiKey) || effectiveApiKey == "YOUR_API_KEY_HERE")
        {
            throw new InvalidOperationException("API key is required. Please configure your Odds API key in the Settings.");
        }

        var cacheKey = $"bookmakers_{effectiveApiKey.GetHashCode()}";

        if (_cache.TryGetValue(cacheKey, out List<BookmakerInfo>? cachedBookmakers))
        {
            return cachedBookmakers!;
        }

        var url = $"{BaseUrl}/sports/soccer_epl/odds?apiKey={effectiveApiKey}&regions=us,uk,eu&markets=h2h&oddsFormat=decimal";
        
        var response = await _httpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        var oddsData = JsonSerializer.Deserialize<List<OddsResponse>>(content);
        
        var bookmakers = oddsData?
            .SelectMany(o => o.Bookmakers)
            .GroupBy(b => b.Key)
            .Select(g => new BookmakerInfo { Key = g.Key, Title = g.First().Title })
            .OrderBy(b => b.Title)
            .ToList() ?? new List<BookmakerInfo>();

        _cache.Set(cacheKey, bookmakers, TimeSpan.FromMinutes(30));
        return bookmakers;
    }

    public async Task<List<OddsResponse>> GetOddsAsync(string sport, string[] markets, string[] bookmakers, string? apiKey = null)
    {
        var effectiveApiKey = apiKey ?? _apiKey;

        if (string.IsNullOrEmpty(effectiveApiKey) || effectiveApiKey == "YOUR_API_KEY_HERE")
        {
            throw new InvalidOperationException("API key is required. Please configure your Odds API key in the Settings.");
        }

        var cacheKey = $"odds_{sport}_{string.Join(",", markets)}_{string.Join(",", bookmakers)}_{effectiveApiKey.GetHashCode()}";

        if (_cache.TryGetValue(cacheKey, out List<OddsResponse>? cachedOdds))
        {
            return cachedOdds!;
        }

        var marketsParam = string.Join(",", markets);
        var bookmakersParam = string.Join(",", bookmakers);

        var url = $"{BaseUrl}/sports/{sport}/odds" +
                  $"?apiKey={effectiveApiKey}" +
                  $"&regions=us,uk,eu" +
                  $"&markets={marketsParam}" +
                  $"&oddsFormat=decimal" +
                  $"&bookmakers={bookmakersParam}";

        var response = await _httpClient.GetAsync(url);
        
        if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
        {
            throw new HttpRequestException("API quota exceeded");
        }
        
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        var odds = JsonSerializer.Deserialize<List<OddsResponse>>(content) ?? new List<OddsResponse>();

        // Cache for 25 seconds (slightly less than refresh interval)
        _cache.Set(cacheKey, odds, TimeSpan.FromSeconds(25));
        
        return odds;
    }

    public Dictionary<string, string> GetSupportedSports() => _supportedSports;
}
