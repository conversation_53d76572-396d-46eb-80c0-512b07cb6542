using Microsoft.Extensions.Caching.Memory;
using Backend.Models;
using System.Text.Json;

namespace Backend.Services;

public class OddsService
{
    private readonly HttpClient _httpClient;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;
    private readonly string _apiKey;
    private const string BaseUrl = "https://api.the-odds-api.com/v4";

    // Supported sports
    private readonly Dictionary<string, string> _supportedSports = new()
    {
        { "soccer_epl", "English Premier League" },
        { "soccer_uefa_champs_league", "UEFA Champions League" },
        { "soccer_uefa_europa_league", "UEFA Europa League" },
        { "soccer_fifa_world_cup", "FIFA World Cup" },
        { "soccer_conmebol_copa_libertadores", "Copa Libertadores" },
        { "basketball_nba", "NBA" },
        { "basketball_ncaab", "NCAA Basketball" },
        { "basketball_euroleague", "EuroLeague" },
        { "basketball_wnba", "WNBA" },
        { "tennis_atp", "ATP Tennis" },
        { "tennis_wta", "WTA Tennis" },
        { "tennis_atp_french_open", "French Open ATP" },
        { "tennis_wta_french_open", "French Open WTA" },
        { "americanfootball_nfl", "NFL" },
        { "americanfootball_ncaaf", "NCAA Football" },
        { "baseball_mlb", "MLB" },
        { "icehockey_nhl", "NHL" },
        { "mma_mixed_martial_arts", "MMA" },
        { "boxing_heavyweight", "Boxing" }
    };

    // Supported markets with descriptions
    private readonly Dictionary<string, MarketInfo> _supportedMarkets = new()
    {
        { "h2h", new MarketInfo("h2h", "Head to Head", "Winner of the match", "🏆") },
        { "spreads", new MarketInfo("spreads", "Point Spread", "Handicap betting", "📊") },
        { "totals", new MarketInfo("totals", "Over/Under", "Total points/goals", "🎯") },
        { "outrights", new MarketInfo("outrights", "Outrights", "Tournament winner", "🏅") },
        { "h2h_lay", new MarketInfo("h2h_lay", "Lay Betting", "Bet against outcome", "❌") },
        { "draw_no_bet", new MarketInfo("draw_no_bet", "Draw No Bet", "Refund if draw", "🤝") },
        { "btts", new MarketInfo("btts", "Both Teams to Score", "Both teams score", "⚽") },
        { "team_totals", new MarketInfo("team_totals", "Team Totals", "Individual team totals", "📈") }
    };

    public OddsService(HttpClient httpClient, IMemoryCache cache, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _cache = cache;
        _configuration = configuration;

        // API key will be provided via request headers
        _apiKey = "YOUR_API_KEY_HERE"; // Default placeholder
    }

    public async Task<List<BookmakerInfo>> GetBookmakersAsync(string? apiKey = null)
    {
        var effectiveApiKey = apiKey ?? _apiKey;

        if (string.IsNullOrEmpty(effectiveApiKey) || effectiveApiKey == "YOUR_API_KEY_HERE")
        {
            throw new InvalidOperationException("API key is required. Please configure your Odds API key in the Settings.");
        }

        var cacheKey = $"bookmakers_{effectiveApiKey.GetHashCode()}";

        if (_cache.TryGetValue(cacheKey, out List<BookmakerInfo>? cachedBookmakers))
        {
            return cachedBookmakers!;
        }

        var url = $"{BaseUrl}/sports/soccer_epl/odds?apiKey={effectiveApiKey}&regions=us,uk,eu&markets=h2h&oddsFormat=decimal";
        
        var response = await _httpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        var oddsData = JsonSerializer.Deserialize<List<OddsResponse>>(content);
        
        var bookmakers = oddsData?
            .SelectMany(o => o.Bookmakers)
            .GroupBy(b => b.Key)
            .Select(g => new BookmakerInfo { Key = g.Key, Title = g.First().Title })
            .OrderBy(b => b.Title)
            .ToList() ?? new List<BookmakerInfo>();

        _cache.Set(cacheKey, bookmakers, TimeSpan.FromMinutes(30));
        return bookmakers;
    }

    public async Task<List<OddsResponse>> GetOddsAsync(string sport, string[] markets, string[] bookmakers, string? apiKey = null)
    {
        var effectiveApiKey = apiKey ?? _apiKey;

        if (string.IsNullOrEmpty(effectiveApiKey) || effectiveApiKey == "YOUR_API_KEY_HERE")
        {
            throw new InvalidOperationException("API key is required. Please configure your Odds API key in the Settings.");
        }

        var cacheKey = $"odds_{sport}_{string.Join(",", markets)}_{string.Join(",", bookmakers)}_{effectiveApiKey.GetHashCode()}";

        if (_cache.TryGetValue(cacheKey, out List<OddsResponse>? cachedOdds))
        {
            return cachedOdds!;
        }

        var marketsParam = string.Join(",", markets);
        var bookmakersParam = string.Join(",", bookmakers);

        var url = $"{BaseUrl}/sports/{sport}/odds" +
                  $"?apiKey={effectiveApiKey}" +
                  $"&regions=us,uk,eu" +
                  $"&markets={marketsParam}" +
                  $"&oddsFormat=decimal" +
                  $"&bookmakers={bookmakersParam}";

        var response = await _httpClient.GetAsync(url);
        
        if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
        {
            throw new HttpRequestException("API quota exceeded");
        }
        
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        var odds = JsonSerializer.Deserialize<List<OddsResponse>>(content) ?? new List<OddsResponse>();

        // Cache for 25 seconds (slightly less than refresh interval)
        _cache.Set(cacheKey, odds, TimeSpan.FromSeconds(25));
        
        return odds;
    }

    public Dictionary<string, string> GetSupportedSports() => _supportedSports;

    public List<SportInfo> GetSportsInfo()
    {
        var sportsWithCategories = new List<SportInfo>
        {
            new() { Key = "soccer_epl", Title = "English Premier League", Category = "Soccer", Icon = "⚽" },
            new() { Key = "soccer_uefa_champs_league", Title = "UEFA Champions League", Category = "Soccer", Icon = "🏆" },
            new() { Key = "soccer_uefa_europa_league", Title = "UEFA Europa League", Category = "Soccer", Icon = "🥈" },
            new() { Key = "soccer_fifa_world_cup", Title = "FIFA World Cup", Category = "Soccer", Icon = "🌍" },
            new() { Key = "soccer_conmebol_copa_libertadores", Title = "Copa Libertadores", Category = "Soccer", Icon = "🏅" },
            new() { Key = "basketball_nba", Title = "NBA", Category = "Basketball", Icon = "🏀" },
            new() { Key = "basketball_ncaab", Title = "NCAA Basketball", Category = "Basketball", Icon = "🎓" },
            new() { Key = "basketball_euroleague", Title = "EuroLeague", Category = "Basketball", Icon = "🇪🇺" },
            new() { Key = "basketball_wnba", Title = "WNBA", Category = "Basketball", Icon = "👩" },
            new() { Key = "tennis_atp", Title = "ATP Tennis", Category = "Tennis", Icon = "🎾" },
            new() { Key = "tennis_wta", Title = "WTA Tennis", Category = "Tennis", Icon = "🎾" },
            new() { Key = "tennis_atp_french_open", Title = "French Open ATP", Category = "Tennis", Icon = "🇫🇷" },
            new() { Key = "tennis_wta_french_open", Title = "French Open WTA", Category = "Tennis", Icon = "🇫🇷" },
            new() { Key = "americanfootball_nfl", Title = "NFL", Category = "American Football", Icon = "🏈" },
            new() { Key = "americanfootball_ncaaf", Title = "NCAA Football", Category = "American Football", Icon = "🎓" },
            new() { Key = "baseball_mlb", Title = "MLB", Category = "Baseball", Icon = "⚾" },
            new() { Key = "icehockey_nhl", Title = "NHL", Category = "Ice Hockey", Icon = "🏒" },
            new() { Key = "mma_mixed_martial_arts", Title = "MMA", Category = "Combat Sports", Icon = "🥊" },
            new() { Key = "boxing_heavyweight", Title = "Boxing", Category = "Combat Sports", Icon = "🥊" }
        };

        return sportsWithCategories;
    }

    public List<MarketInfo> GetMarketsInfo() => _supportedMarkets.Values.ToList();
}
