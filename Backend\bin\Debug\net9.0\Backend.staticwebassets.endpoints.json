{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000200561572"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4985"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c=\""}, {"Name": "ETag", "Value": "W/\"+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:33:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22051"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:32:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4985"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:33:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c="}]}, {"Route": "app.k5kwazxvlu.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000200561572"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4985"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c=\""}, {"Name": "ETag", "Value": "W/\"+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:33:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k5kwazxvlu"}, {"Name": "integrity", "Value": "sha256-+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.k5kwazxvlu.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22051"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:32:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k5kwazxvlu"}, {"Name": "integrity", "Value": "sha256-+EvsMtP6y0ztRahuk+Ymdqpjbtd5I7+julJcHCUWDvc="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.k5kwazxvlu.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4985"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:33:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k5kwazxvlu"}, {"Name": "integrity", "Value": "sha256-jVIR2dLBhUoBg3XQSbzYhn441TCCR6jTbDu70P4vS8c="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476644423"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "ETag", "Value": "W/\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:19:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880="}]}, {"Route": "index.tfkhlfu4gj.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476644423"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "ETag", "Value": "W/\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfkhlfu4gj"}, {"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.tfkhlfu4gj.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:19:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfkhlfu4gj"}, {"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.tfkhlfu4gj.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfkhlfu4gj"}, {"Name": "integrity", "Value": "sha256-Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000198688655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "ETag", "Value": "W/\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "29767"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:27:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk="}]}, {"Route": "styles.s43tkxfxao.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000198688655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "ETag", "Value": "W/\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s43tkxfxao"}, {"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.s43tkxfxao.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "29767"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:27:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s43tkxfxao"}, {"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.s43tkxfxao.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s43tkxfxao"}, {"Name": "integrity", "Value": "sha256-qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk="}, {"Name": "label", "Value": "styles.css.gz"}]}]}