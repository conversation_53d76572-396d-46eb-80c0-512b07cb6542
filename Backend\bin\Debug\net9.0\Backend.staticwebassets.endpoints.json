{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000288933834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "ETag", "Value": "W/\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws="}]}, {"Route": "app.o58y1iq1o7.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000288933834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "ETag", "Value": "W/\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58y1iq1o7"}, {"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.o58y1iq1o7.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58y1iq1o7"}, {"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.o58y1iq1o7.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58y1iq1o7"}, {"Name": "integrity", "Value": "sha256-YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000720461095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "ETag", "Value": "W/\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4957"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw="}]}, {"Route": "index.ts83a7p56f.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000720461095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "ETag", "Value": "W/\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ts83a7p56f"}, {"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.ts83a7p56f.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4957"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ts83a7p56f"}, {"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.ts83a7p56f.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ts83a7p56f"}, {"Name": "integrity", "Value": "sha256-KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000527704485"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "ETag", "Value": "W/\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7449"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo="}]}, {"Route": "styles.icexkurat2.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000527704485"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "ETag", "Value": "W/\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "icexkurat2"}, {"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.icexkurat2.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7449"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "icexkurat2"}, {"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.icexkurat2.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "icexkurat2"}, {"Name": "integrity", "Value": "sha256-so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo="}, {"Name": "label", "Value": "styles.css.gz"}]}]}