{"Version": 1, "Hash": "hOlO6WVBVwxDbQ00THxSTLxpXsoMkPHsVdWqWuH6fRc=", "Source": "Backend", "BasePath": "_content/Backend", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Backend\\wwwroot", "Source": "Backend", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "Pattern": "**"}], "Assets": [{"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-1brjmvz4go.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=1brjmvz4go}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yngi22ycfl", "Integrity": "3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "FileLength": 1789, "LastWriteTime": "2025-06-27T11:04:11+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-6cmviv2ofs.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=6cmviv2ofs}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "asnzor874d", "Integrity": "KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "FileLength": 3114, "LastWriteTime": "2025-06-27T11:04:11+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-8em28t5j70.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=8em28t5j70}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n5hk0gs631", "Integrity": "2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "FileLength": 1242, "LastWriteTime": "2025-06-27T11:04:11+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6cmviv2ofs", "Integrity": "1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 11308, "LastWriteTime": "2025-06-27T10:55:42+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8em28t5j70", "Integrity": "4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 4626, "LastWriteTime": "2025-06-27T10:53:19+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1brjmvz4go", "Integrity": "/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 6740, "LastWriteTime": "2025-06-27T10:54:00+00:00"}], "Endpoints": [{"Route": "app.6cmviv2ofs.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-6cmviv2ofs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000321027287"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cmviv2ofs"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}]}, {"Route": "app.6cmviv2ofs.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:55:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cmviv2ofs"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}]}, {"Route": "app.6cmviv2ofs.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-6cmviv2ofs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cmviv2ofs"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo="}]}, {"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-6cmviv2ofs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000321027287"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}]}, {"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:55:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1tBAQCylINFiK8ufs0ntE2xH48e73qFHwUO7AoDr3Qc="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-6cmviv2ofs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3114"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KgeEDyGPDZp0bY2qbtSuTMq059jyrRRac/YTTeAEaLo="}]}, {"Route": "index.8em28t5j70.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-8em28t5j70.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000804505229"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8em28t5j70"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}]}, {"Route": "index.8em28t5j70.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4626"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:53:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8em28t5j70"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}]}, {"Route": "index.8em28t5j70.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-8em28t5j70.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8em28t5j70"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-8em28t5j70.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000804505229"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4626"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:53:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4IkEPAzuUKdJG6IlAQjZyOvxRq8F0f533UbeyhdvEQU="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-8em28t5j70.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1242"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2xM7PXAQRWkvQBYVW1ok9d5V2/oGdQl65fmYeMo1DbQ="}]}, {"Route": "styles.1brjmvz4go.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-1brjmvz4go.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000558659218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1brjmvz4go"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}]}, {"Route": "styles.1brjmvz4go.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6740"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:54:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1brjmvz4go"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}]}, {"Route": "styles.1brjmvz4go.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-1brjmvz4go.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1brjmvz4go"}, {"Name": "label", "Value": "styles.css.gz"}, {"Name": "integrity", "Value": "sha256-3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-1brjmvz4go.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000558659218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6740"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 10:54:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/fJD2E5bH0gi8YX3mU9TwW6GSYcT7OZ18dMvpnBm5+s="}]}, {"Route": "styles.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-1brjmvz4go.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1789"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:04:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3s5j0t6BWCIehkfM5oTS+pLnXCJHEn3b+aPkR8WIeBE="}]}]}