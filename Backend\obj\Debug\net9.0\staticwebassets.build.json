{"Version": 1, "Hash": "xf5PhsCrw0W2xsDru2yQfZpbCcrkfb/5lftsKQwfkM0=", "Source": "Backend", "BasePath": "_content/Backend", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Backend\\wwwroot", "Source": "Backend", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "Pattern": "**"}], "Assets": [{"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-s43tkxfxao.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=s43tkxfxao}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4180wz4ydu", "Integrity": "qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "FileLength": 5032, "LastWriteTime": "2025-06-27T11:28:06+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-v4fd2gfz2m.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=v4fd2gfz2m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svcadqd7zw", "Integrity": "uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "FileLength": 4882, "LastWriteTime": "2025-06-27T11:28:06+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-tfkhlfu4gj.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=tfkhlfu4gj}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3gmhnk4t0p", "Integrity": "Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "FileLength": 2097, "LastWriteTime": "2025-06-27T11:28:06+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v4fd2gfz2m", "Integrity": "378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 21388, "LastWriteTime": "2025-06-27T11:27:31+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tfkhlfu4gj", "Integrity": "R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 10642, "LastWriteTime": "2025-06-27T11:19:34+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s43tkxfxao", "Integrity": "5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 29767, "LastWriteTime": "2025-06-27T11:27:45+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-v4fd2gfz2m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000204792136"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4882"}, {"Name": "ETag", "Value": "\"uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA="}]}, {"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:27:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-v4fd2gfz2m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4882"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4="}]}, {"Route": "app.v4fd2gfz2m.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-v4fd2gfz2m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000204792136"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4882"}, {"Name": "ETag", "Value": "\"uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v4fd2gfz2m"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA="}]}, {"Route": "app.v4fd2gfz2m.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:27:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v4fd2gfz2m"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-378V50xD/kF2qze0VqnSUFC375UIB+fJFZGYB2/XUOA="}]}, {"Route": "app.v4fd2gfz2m.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-v4fd2gfz2m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4882"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v4fd2gfz2m"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-uMTjn2REuZot+ALrhfGmwNBcJ309eYe9s3K75tcLGP4="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-tfkhlfu4gj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476644423"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:19:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-tfkhlfu4gj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880="}]}, {"Route": "index.tfkhlfu4gj.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-tfkhlfu4gj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476644423"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfkhlfu4gj"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}]}, {"Route": "index.tfkhlfu4gj.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:19:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfkhlfu4gj"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-R81gXTlWqo/XDi3wFQHvGHaPp+6w1t7JpLhRBq8ldyM="}]}, {"Route": "index.tfkhlfu4gj.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-tfkhlfu4gj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2097"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfkhlfu4gj"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-Ha2jOG0/1qi1Q//ZBbhpXOi6bcS6xrKoNX7P3uxC880="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-s43tkxfxao.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000198688655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29767"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:27:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}]}, {"Route": "styles.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-s43tkxfxao.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk="}]}, {"Route": "styles.s43tkxfxao.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-s43tkxfxao.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000198688655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s43tkxfxao"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}]}, {"Route": "styles.s43tkxfxao.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29767"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:27:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s43tkxfxao"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-5GKJllYXA97HEDahadkMyh3kxRxZnJq5Sfwwt8NkOGY="}]}, {"Route": "styles.s43tkxfxao.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-s43tkxfxao.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:28:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s43tkxfxao"}, {"Name": "label", "Value": "styles.css.gz"}, {"Name": "integrity", "Value": "sha256-qHTu3O6g2OEZr0vk8shjVh7FCsV3GFp4BUnBzdggszk="}]}]}