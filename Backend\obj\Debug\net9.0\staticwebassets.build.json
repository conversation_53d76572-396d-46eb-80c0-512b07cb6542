{"Version": 1, "Hash": "30WdV1PdH4aWDzNg/VcYlW4VoCZt6pDJAbw1wyc7m0o=", "Source": "Backend", "BasePath": "_content/Backend", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Backend\\wwwroot", "Source": "Backend", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "Pattern": "**"}], "Assets": [{"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-iq6iwg8y1j.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=iq6iwg8y1j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8d2tdptwjh", "Integrity": "xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "FileLength": 5530, "LastWriteTime": "2025-06-27T11:58:41+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-zd1wu8imt9.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=zd1wu8imt9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "69ukul3l90", "Integrity": "KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "FileLength": 6219, "LastWriteTime": "2025-06-27T11:58:41+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-hogd10aeqz.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=hogd10aeqz}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1gfv02w8g1", "Integrity": "D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "FileLength": 2619, "LastWriteTime": "2025-06-27T11:58:41+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zd1wu8imt9", "Integrity": "B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 27232, "LastWriteTime": "2025-06-27T11:57:42+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hogd10aeqz", "Integrity": "T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 13608, "LastWriteTime": "2025-06-27T11:55:35+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iq6iwg8y1j", "Integrity": "mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 33647, "LastWriteTime": "2025-06-27T11:54:59+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-zd1wu8imt9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160771704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27232"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:57:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-zd1wu8imt9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON><PERSON>jwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE="}]}, {"Route": "app.zd1wu8imt9.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-zd1wu8imt9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160771704"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.zd1wu8imt9.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27232"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:57:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-B22W1GpzWQod0jl3VtjraVLyU4Dli5lrPlKtXWai1xU="}]}, {"Route": "app.zd1wu8imt9.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-zd1wu8imt9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KpjwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zd1wu8imt9"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-<PERSON><PERSON>jwuEJmUxF6RCY/bFcsD8gF9drQ2D3VXTBCHBYUyZE="}]}, {"Route": "index.hogd10aeqz.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-hogd10aeqz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000381679389"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.hogd10aeqz.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13608"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:55:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.hogd10aeqz.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-hogd10aeqz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hogd10aeqz"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-hogd10aeqz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000381679389"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13608"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:55:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6movoiK4sCTGMMEhFB93RYr3tIluGNWPEZRmL6DaHE="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-hogd10aeqz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2619"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D1isRu/UnVXbYtDInVlKc+g0Dxt//oX/q0OtgWfHt5o="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-iq6iwg8y1j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000180799132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33647"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:54:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}]}, {"Route": "styles.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-iq6iwg8y1j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8="}]}, {"Route": "styles.iq6iwg8y1j.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-iq6iwg8y1j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000180799132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iq6iwg8y1j"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}]}, {"Route": "styles.iq6iwg8y1j.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33647"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:54:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iq6iwg8y1j"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-mlJV7UaivXZ8LezhK6Q/WNEqpkgarRRAVz6R7dnCjY0="}]}, {"Route": "styles.iq6iwg8y1j.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-iq6iwg8y1j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5530"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:58:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iq6iwg8y1j"}, {"Name": "label", "Value": "styles.css.gz"}, {"Name": "integrity", "Value": "sha256-xE0Qz5IT28PlIUmGZJjywjIEo0wMuUexvH+qGEZ3Ef8="}]}]}