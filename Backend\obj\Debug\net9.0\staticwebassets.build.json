{"Version": 1, "Hash": "wDZsvxzPzOrq3YqWfJi/YARPvfIJhTgWeTY1ZpZmSAk=", "Source": "Backend", "BasePath": "_content/Backend", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Backend\\wwwroot", "Source": "Backend", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "Pattern": "**"}], "Assets": [{"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-icexkurat2.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint=icexkurat2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qlw5q5n5s9", "Integrity": "so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "FileLength": 1894, "LastWriteTime": "2025-06-27T11:09:33+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-o58y1iq1o7.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint=o58y1iq1o7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qi4u0k21dv", "Integrity": "YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "FileLength": 3460, "LastWriteTime": "2025-06-27T11:09:33+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-ts83a7p56f.gz", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint=ts83a7p56f}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zx53hegtxr", "Integrity": "KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "FileLength": 1387, "LastWriteTime": "2025-06-27T11:09:33+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o58y1iq1o7", "Integrity": "zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 13079, "LastWriteTime": "2025-06-27T11:08:22+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ts83a7p56f", "Integrity": "sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 4957, "LastWriteTime": "2025-06-27T11:09:17+00:00"}, {"Identity": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "SourceId": "Backend", "SourceType": "Discovered", "ContentRoot": "C:\\WinSpread\\Backend\\wwwroot\\", "BasePath": "_content/Backend", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "icexkurat2", "Integrity": "4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 7449, "LastWriteTime": "2025-06-27T11:08:39+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-o58y1iq1o7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000288933834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}]}, {"Route": "app.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-o58y1iq1o7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws="}]}, {"Route": "app.o58y1iq1o7.js", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-o58y1iq1o7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000288933834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58y1iq1o7"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}]}, {"Route": "app.o58y1iq1o7.js", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58y1iq1o7"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-zGYrVZednChz9N89rYy6Ib5utcy6FkZBM5lr/6NhKWA="}]}, {"Route": "app.o58y1iq1o7.js.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\dw4z07l20y-o58y1iq1o7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o58y1iq1o7"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-YqKfI5MGqpZEvfSSOh1G+/eKTZ0lbKH5r9feMBzeNws="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-ts83a7p56f.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000720461095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}]}, {"Route": "index.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4957"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-ts83a7p56f.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw="}]}, {"Route": "index.ts83a7p56f.html", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-ts83a7p56f.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000720461095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ts83a7p56f"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}]}, {"Route": "index.ts83a7p56f.html", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4957"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ts83a7p56f"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-sxqBVXIEZh+jZx21PvxZH7IPotjDOSXXH77e46Gg4v0="}]}, {"Route": "index.ts83a7p56f.html.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\for7mogo9r-ts83a7p56f.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1387"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ts83a7p56f"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-KdYpGiUFR6+jDtWUfPcZHqKPwUmLBeUN7gjQC3XL/Hw="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-icexkurat2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000527704485"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}]}, {"Route": "styles.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7449"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}]}, {"Route": "styles.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-icexkurat2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo="}]}, {"Route": "styles.icexkurat2.css", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-icexkurat2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000527704485"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "icexkurat2"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}]}, {"Route": "styles.icexkurat2.css", "AssetFile": "C:\\WinSpread\\Backend\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7449"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:08:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "icexkurat2"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-4sJBUJ70D7U6XvWqDYwW8UqXitO7QBE/em9LP9MMzWE="}]}, {"Route": "styles.icexkurat2.css.gz", "AssetFile": "C:\\WinSpread\\Backend\\obj\\Debug\\net9.0\\compressed\\9xogwy9he7-icexkurat2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1894"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 11:09:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "icexkurat2"}, {"Name": "label", "Value": "styles.css.gz"}, {"Name": "integrity", "Value": "sha256-so1ancHcZlKodTIMwJaQ8B+X9q8BFXI5FY3wiEi4/Zo="}]}]}