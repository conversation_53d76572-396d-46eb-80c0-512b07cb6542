/* Dark Greyscale CSS Variables */
:root {
    /* Brand Colors - Greyscale */
    --primary: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    --primary-solid: #6b7280;
    --primary-light: rgba(107, 114, 128, 0.15);
    --secondary: #9ca3af;
    --accent: #d1d5db;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;

    /* Dark Greyscale Palette */
    --gray-50: #18181b;
    --gray-100: #27272a;
    --gray-200: #3f3f46;
    --gray-300: #52525b;
    --gray-400: #71717a;
    --gray-500: #a1a1aa;
    --gray-600: #d4d4d8;
    --gray-700: #e4e4e7;
    --gray-800: #f4f4f5;
    --gray-900: #fafafa;

    /* Dark Theme Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --text-primary: #f4f4f5;
    --text-secondary: #d4d4d8;
    --text-tertiary: #a1a1aa;
    --border-color: #27272a;
    --border-light: #3f3f46;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
}

[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-solid: #667eea;
    --primary-light: rgba(102, 126, 234, 0.1);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
}

/* Selection */
::selection {
    background: var(--primary-light);
    color: var(--primary-solid);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--gray-600);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Layout */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* Navigation */
.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--space-4) var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
}

.brand-text h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.brand-text span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    background: var(--primary-light);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    margin-left: var(--space-2);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 380px 1fr;
    min-height: calc(100vh - 80px);
}

/* Control Panel */
.control-panel {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: var(--space-6);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.panel-section {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
    flex-wrap: wrap;
    gap: var(--space-2);
}

.section-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.section-badge {
    background: var(--primary-light);
    color: var(--primary-solid);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.select-all-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all 0.2s ease;
}

.select-all-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-solid);
    color: var(--primary-solid);
}

/* Grid Layouts */
.sports-grid,
.markets-grid,
.bookmakers-grid {
    display: grid;
    gap: var(--space-3);
}

.sports-grid {
    grid-template-columns: 1fr;
}

.markets-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
}

.bookmakers-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

/* Selection Cards */
.selection-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.selection-card:hover {
    border-color: var(--primary-solid);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.selection-card.selected {
    border-color: var(--primary-solid);
    background: var(--primary-light);
    box-shadow: var(--shadow-md);
}

.selection-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
}

.selection-card .card-icon {
    font-size: 1.5rem;
    margin-bottom: var(--space-2);
    display: block;
}

.selection-card .card-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-1);
    line-height: 1.3;
}

.selection-card .card-subtitle {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.selection-card .card-description {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    margin-top: var(--space-1);
    line-height: 1.4;
}

/* Sport Cards */
.sport-card {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.2s ease;
}

.sport-card:hover {
    border-color: var(--primary-solid);
    background: var(--primary-light);
}

.sport-card.selected {
    border-color: var(--primary-solid);
    background: var(--primary-light);
    box-shadow: var(--shadow-sm);
}

.sport-card .sport-icon {
    font-size: 1.25rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.sport-card.selected .sport-icon {
    background: var(--primary-solid);
    color: white;
}

.sport-card .sport-info {
    flex: 1;
}

.sport-card .sport-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    line-height: 1.3;
}

.sport-card .sport-category {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Category Sections */
.category-section {
    margin-bottom: var(--space-4);
}

.category-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-3);
    padding-left: var(--space-2);
    border-left: 3px solid var(--primary-solid);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-5);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-ghost:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-solid);
    color: var(--primary-solid);
}

.btn-icon {
    font-size: 1rem;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border: 3px solid var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    font-size: var(--font-size-lg);
    font-family: var(--font-family);
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    min-height: 56px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-solid);
    box-shadow: 0 0 0 4px var(--primary-light), var(--shadow-md);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--text-tertiary);
    font-weight: 500;
}

.form-control:hover {
    border-color: var(--gray-300);
    box-shadow: var(--shadow-md);
}

/* Results Dashboard */
.results-dashboard {
    padding: var(--space-6);
    overflow-y: auto;
    background: var(--bg-primary);
}

.dashboard-header {
    margin-bottom: var(--space-8);
}

.header-info {
    margin-bottom: var(--space-6);
}

.header-info h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-400);
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: var(--success);
}

.status-dot.error {
    background: var(--danger);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    text-align: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card.highlight {
    background: var(--primary-light);
    border-color: var(--primary-solid);
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.stat-card.highlight .stat-value {
    color: var(--primary-solid);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Welcome State */
.welcome-state {
    text-align: center;
    padding: var(--space-12);
    max-width: 600px;
    margin: 0 auto;
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--space-6);
    display: block;
}

.welcome-state h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

.welcome-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.step {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    text-align: left;
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.step-number {
    width: 32px;
    height: 32px;
    background: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.step-text {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.welcome-cta {
    background: var(--primary);
    color: white;
    border: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-lg);
}

.welcome-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Loading Skeletons */
.loading-skeleton {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.skeleton-item {
    height: 60px;
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-lg);
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Modern Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    animation: modalFadeIn 0.3s ease;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
}

.modal-content {
    position: relative;
    background: var(--bg-primary);
    margin: 3vh auto;
    width: 95%;
    max-width: 700px;
    max-height: 90vh;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
    border: 2px solid var(--border-color);
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from { transform: translateY(-20px) scale(0.95); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.modal-header {
    padding: var(--space-8);
    border-bottom: 2px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
}

.modal-title {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.modal-icon {
    width: 56px;
    height: 56px;
    background: var(--primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    color: white;
}

.modal-title h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
}

.close-btn:hover {
    background: var(--danger);
    border-color: var(--danger);
    color: white;
    transform: scale(1.05);
}

.modal-body {
    padding: var(--space-8);
    background: var(--bg-primary);
    min-height: 400px;
}

.modal-footer {
    padding: var(--space-8);
    border-top: 2px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-4);
    background: var(--bg-secondary);
}

/* Enhanced Settings Styles */
.settings-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-10);
    max-height: 65vh;
    overflow-y: auto;
    padding: var(--space-4);
    background: var(--bg-primary);
}

.settings-section::-webkit-scrollbar {
    width: 8px;
}

.settings-section::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.settings-section::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-md);
}

.settings-section::-webkit-scrollbar-thumb:hover {
    background: var(--gray-300);
}

.setting-item {
    padding: var(--space-8);
    border: 3px solid var(--border-color);
    border-radius: var(--radius-2xl);
    background: var(--bg-secondary);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 120px;
    box-shadow: var(--shadow-md);
}

.setting-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.setting-item:hover::before,
.setting-item.priority::before {
    opacity: 1;
}

.setting-item.priority {
    border-color: var(--success);
    background: rgba(16, 185, 129, 0.05);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.setting-item.priority::before {
    background: var(--success);
}

.setting-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-solid);
}

.setting-header {
    display: flex;
    align-items: flex-start;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.setting-icon {
    width: 64px;
    height: 64px;
    background: var(--primary);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-lg);
    color: white;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.setting-info {
    flex: 1;
    min-height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.setting-info label {
    font-weight: 800;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    display: block;
    margin-bottom: var(--space-3);
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.setting-description {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    font-weight: 500;
}

.setting-content {
    margin-top: var(--space-6);
    background: var(--bg-primary);
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-light);
}

.setting-help {
    margin-top: var(--space-6);
    padding: var(--space-6);
    background: var(--bg-tertiary);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    border: 2px solid var(--border-color);
    border-left: 6px solid var(--primary-solid);
    box-shadow: var(--shadow-sm);
}

.setting-help a {
    color: var(--primary-solid);
    text-decoration: none;
    font-weight: 700;
    transition: all 0.2s ease;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    background: rgba(107, 114, 128, 0.1);
}

.setting-help a:hover {
    text-decoration: underline;
    color: white;
    background: var(--primary-solid);
    transform: translateY(-1px);
}

/* Capital Input Group */
.capital-input-group {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.capital-input {
    flex: 1;
    max-width: 250px;
    font-size: var(--font-size-lg);
    font-weight: 600;
    text-align: center;
}

.currency-symbol {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--success);
    background: rgba(16, 185, 129, 0.15);
    padding: var(--space-4) var(--space-5);
    border-radius: var(--radius-xl);
    border: 3px solid rgba(16, 185, 129, 0.3);
    box-shadow: var(--shadow-md);
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.capital-presets {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.capital-preset {
    padding: var(--space-3) var(--space-4);
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    text-align: center;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.capital-preset:hover {
    background: var(--success);
    border-color: var(--success);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Range Input */
.range-input {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-light);
}

.range-slider {
    flex: 1;
    height: 8px;
    border-radius: var(--radius-md);
    background: var(--bg-tertiary);
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-solid);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    border: 3px solid white;
    transition: all 0.2s ease;
}

.range-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.range-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-solid);
    cursor: pointer;
    border: 3px solid white;
    box-shadow: var(--shadow-lg);
}

.range-value {
    width: 80px;
    padding: var(--space-3);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    text-align: center;
    font-size: var(--font-size-base);
    font-weight: 600;
}

.range-unit {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    font-weight: 600;
    min-width: 60px;
}

/* Theme Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-light);
}

.theme-checkbox {
    display: none;
}

.theme-label {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    cursor: pointer;
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.theme-label:hover {
    background: var(--bg-primary);
    transform: scale(1.02);
}

.theme-option {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    transition: all 0.3s ease;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

.theme-slider {
    width: 64px;
    height: 32px;
    background: var(--bg-tertiary);
    border-radius: 16px;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.theme-slider::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border-color);
}

.theme-checkbox:checked + .theme-label .theme-slider {
    background: var(--primary-solid);
    border-color: var(--primary-solid);
}

.theme-checkbox:checked + .theme-label .theme-slider::before {
    transform: translateX(30px);
    background: white;
    border-color: white;
}

.theme-checkbox:checked + .theme-label .theme-option:last-child {
    color: var(--primary-solid);
    font-weight: 700;
}

.theme-checkbox:not(:checked) + .theme-label .theme-option:first-child {
    color: var(--text-primary);
    font-weight: 700;
}

/* Results Table */
.results-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.results-table th,
.results-table td {
    padding: var(--space-4) var(--space-5);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.results-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.results-table tr.arbitrage {
    background: rgba(16, 185, 129, 0.05);
    border-left: 4px solid var(--success);
}

.results-table tr:hover {
    background: var(--bg-secondary);
}

.match-row {
    cursor: pointer;
    transition: all 0.2s ease;
}

.match-row:hover {
    background: var(--bg-tertiary);
}

.expand-arrow {
    transition: transform 0.2s ease;
    font-size: var(--font-size-lg);
    color: var(--text-tertiary);
    margin-right: var(--space-2);
}

.match-row.expanded .expand-arrow {
    transform: rotate(180deg);
}

.match-details {
    display: none;
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
}

.match-details.expanded {
    display: table-row;
}

.match-details td {
    padding: var(--space-6);
}

.sportsbook-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.sportsbook-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.sportsbook-link:hover {
    border-color: var(--primary-solid);
    background: var(--primary-light);
    transform: translateY(-1px);
}

.sportsbook-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.sportsbook-name {
    font-weight: 600;
    font-size: var(--font-size-base);
}

.sportsbook-odds {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.external-icon {
    font-size: var(--font-size-lg);
    color: var(--text-tertiary);
}

.roi-positive {
    color: var(--success);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.roi-neutral {
    color: var(--text-secondary);
}

.stake-plan {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.stake-plan-item {
    margin-bottom: var(--space-1);
    padding: var(--space-1) var(--space-2);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    display: inline-block;
    margin-right: var(--space-2);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.toast {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastSlideIn 0.3s ease;
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
}

.toast.error::before {
    background: var(--danger);
}

.toast.success::before {
    background: var(--success);
}

.toast.warning::before {
    background: var(--warning);
}

@keyframes toastSlideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Error States */
.api-key-prompt,
.error {
    text-align: center;
    padding: var(--space-8);
    background: var(--bg-primary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-xl);
    margin: var(--space-4) 0;
}

.api-key-prompt {
    border-color: var(--warning);
    background: rgba(245, 158, 11, 0.05);
}

.error {
    border-color: var(--danger);
    background: rgba(239, 68, 68, 0.05);
}

.api-key-prompt p:first-child,
.error p:first-child {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.api-key-prompt p:first-child {
    color: var(--warning);
}

.error p:first-child {
    color: var(--danger);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 320px 1fr;
    }

    .control-panel {
        padding: var(--space-4);
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .control-panel {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        max-height: 50vh;
    }

    .navbar {
        padding: var(--space-3) var(--space-4);
        flex-direction: column;
        gap: var(--space-3);
    }

    .nav-controls {
        width: 100%;
        justify-content: center;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .welcome-steps {
        gap: var(--space-3);
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .modal-content {
        margin: 2vh auto;
        width: 95%;
    }

    .results-table {
        font-size: var(--font-size-sm);
    }

    .results-table th,
    .results-table td {
        padding: var(--space-2) var(--space-3);
    }
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Results Table */
.results-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.results-table th,
.results-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--text-color);
}

.results-table tr.arbitrage {
    background: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.roi-positive {
    color: var(--success-color);
    font-weight: 600;
}

.roi-neutral {
    color: var(--secondary-color);
}

.stake-plan {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.stake-plan-item {
    margin-bottom: 0.25rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group small {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
}

.toast {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Loading and Empty States */
.loading {
    text-align: center;
    color: var(--secondary-color);
    padding: 2rem;
}

.empty-state {
    text-align: center;
    color: var(--secondary-color);
    padding: 3rem;
}

.api-key-prompt {
    text-align: center;
    padding: 2rem;
    background: var(--card-bg);
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    margin: 1rem 0;
}

.api-key-prompt p:first-child {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--warning-color);
    margin-bottom: 0.5rem;
}

.api-key-prompt p:last-of-type {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.error {
    text-align: center;
    padding: 2rem;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: 8px;
    margin: 1rem 0;
}

.error p:first-child {
    font-weight: 600;
    color: var(--danger-color);
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .sidebar {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-controls {
        width: 100%;
        justify-content: center;
    }
    
    .results-table {
        font-size: 0.8rem;
    }
    
    .results-table th,
    .results-table td {
        padding: 0.5rem;
    }
}
