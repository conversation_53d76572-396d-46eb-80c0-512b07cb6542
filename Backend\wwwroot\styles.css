/* Modern CSS Variables */
:root {
    /* Brand Colors */
    --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-solid: #667eea;
    --primary-light: rgba(102, 126, 234, 0.1);
    --secondary: #8b5cf6;
    --accent: #06d6a0;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
}

[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-color: #334155;
    --border-light: #475569;
    --primary-light: rgba(102, 126, 234, 0.2);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
}

/* Selection */
::selection {
    background: var(--primary-light);
    color: var(--primary-solid);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--gray-600);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Layout */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* Navigation */
.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--space-4) var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
}

.brand-text h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.brand-text span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    background: var(--primary-light);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    margin-left: var(--space-2);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 380px 1fr;
    min-height: calc(100vh - 80px);
}

/* Control Panel */
.control-panel {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: var(--space-6);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.panel-section {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
    flex-wrap: wrap;
    gap: var(--space-2);
}

.section-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.section-badge {
    background: var(--primary-light);
    color: var(--primary-solid);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.select-all-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all 0.2s ease;
}

.select-all-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-solid);
    color: var(--primary-solid);
}

/* Grid Layouts */
.sports-grid,
.markets-grid,
.bookmakers-grid {
    display: grid;
    gap: var(--space-3);
}

.sports-grid {
    grid-template-columns: 1fr;
}

.markets-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
}

.bookmakers-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

/* Selection Cards */
.selection-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.selection-card:hover {
    border-color: var(--primary-solid);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.selection-card.selected {
    border-color: var(--primary-solid);
    background: var(--primary-light);
    box-shadow: var(--shadow-md);
}

.selection-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
}

.selection-card .card-icon {
    font-size: 1.5rem;
    margin-bottom: var(--space-2);
    display: block;
}

.selection-card .card-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-1);
    line-height: 1.3;
}

.selection-card .card-subtitle {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.selection-card .card-description {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    margin-top: var(--space-1);
    line-height: 1.4;
}

/* Sport Cards */
.sport-card {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.2s ease;
}

.sport-card:hover {
    border-color: var(--primary-solid);
    background: var(--primary-light);
}

.sport-card.selected {
    border-color: var(--primary-solid);
    background: var(--primary-light);
    box-shadow: var(--shadow-sm);
}

.sport-card .sport-icon {
    font-size: 1.25rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.sport-card.selected .sport-icon {
    background: var(--primary-solid);
    color: white;
}

.sport-card .sport-info {
    flex: 1;
}

.sport-card .sport-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    line-height: 1.3;
}

.sport-card .sport-category {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Category Sections */
.category-section {
    margin-bottom: var(--space-4);
}

.category-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-3);
    padding-left: var(--space-2);
    border-left: 3px solid var(--primary-solid);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-5);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-ghost:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-solid);
    color: var(--primary-solid);
}

.btn-icon {
    font-size: 1rem;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-solid);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-control::placeholder {
    color: var(--text-tertiary);
}

/* Results Dashboard */
.results-dashboard {
    padding: var(--space-6);
    overflow-y: auto;
    background: var(--bg-primary);
}

.dashboard-header {
    margin-bottom: var(--space-8);
}

.header-info {
    margin-bottom: var(--space-6);
}

.header-info h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-400);
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: var(--success);
}

.status-dot.error {
    background: var(--danger);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    text-align: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card.highlight {
    background: var(--primary-light);
    border-color: var(--primary-solid);
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.stat-card.highlight .stat-value {
    color: var(--primary-solid);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Welcome State */
.welcome-state {
    text-align: center;
    padding: var(--space-12);
    max-width: 600px;
    margin: 0 auto;
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--space-6);
    display: block;
}

.welcome-state h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

.welcome-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.step {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    text-align: left;
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.step-number {
    width: 32px;
    height: 32px;
    background: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.step-text {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.welcome-cta {
    background: var(--primary);
    color: white;
    border: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-lg);
}

.welcome-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Loading Skeletons */
.loading-skeleton {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.skeleton-item {
    height: 60px;
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-lg);
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Modern Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    animation: modalFadeIn 0.3s ease;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: var(--bg-primary);
    margin: 5vh auto;
    width: 90%;
    max-width: 600px;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from { transform: translateY(-20px) scale(0.95); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.modal-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-light);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.modal-title h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* Settings Styles */
.settings-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.setting-item {
    padding: var(--space-5);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--bg-secondary);
}

.setting-item.priority {
    border-color: var(--primary-solid);
    background: var(--primary-light);
}

.setting-header {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.setting-icon {
    width: 32px;
    height: 32px;
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.setting-info {
    flex: 1;
}

.setting-info label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    display: block;
    margin-bottom: var(--space-1);
}

.setting-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.setting-help {
    margin-top: var(--space-3);
    padding: var(--space-3);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

.setting-help a {
    color: var(--primary-solid);
    text-decoration: none;
    font-weight: 500;
}

.setting-help a:hover {
    text-decoration: underline;
}

/* Range Input */
.range-input {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.range-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-tertiary);
    outline: none;
    -webkit-appearance: none;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-solid);
    cursor: pointer;
    box-shadow: var(--shadow-md);
}

.range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-solid);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-md);
}

.range-value {
    width: 60px;
    padding: var(--space-2);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    text-align: center;
    font-size: var(--font-size-sm);
}

.range-unit {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Theme Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
}

.theme-checkbox {
    display: none;
}

.theme-label {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
}

.theme-label:hover {
    background: var(--bg-primary);
}

.theme-option {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.theme-slider {
    width: 50px;
    height: 26px;
    background: var(--bg-tertiary);
    border-radius: 13px;
    position: relative;
    transition: all 0.3s ease;
}

.theme-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.theme-checkbox:checked + .theme-label .theme-slider {
    background: var(--primary-solid);
}

.theme-checkbox:checked + .theme-label .theme-slider::before {
    transform: translateX(24px);
}

.theme-checkbox:checked + .theme-label .theme-option:last-child {
    color: var(--primary-solid);
    font-weight: 600;
}

.theme-checkbox:not(:checked) + .theme-label .theme-option:first-child {
    color: var(--text-primary);
    font-weight: 600;
}

/* Results Table */
.results-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.results-table th,
.results-table td {
    padding: var(--space-4) var(--space-5);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.results-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.results-table tr.arbitrage {
    background: rgba(16, 185, 129, 0.05);
    border-left: 4px solid var(--success);
}

.results-table tr:hover {
    background: var(--bg-secondary);
}

.roi-positive {
    color: var(--success);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.roi-neutral {
    color: var(--text-secondary);
}

.stake-plan {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.stake-plan-item {
    margin-bottom: var(--space-1);
    padding: var(--space-1) var(--space-2);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    display: inline-block;
    margin-right: var(--space-2);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.toast {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastSlideIn 0.3s ease;
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
}

.toast.error::before {
    background: var(--danger);
}

.toast.success::before {
    background: var(--success);
}

.toast.warning::before {
    background: var(--warning);
}

@keyframes toastSlideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Error States */
.api-key-prompt,
.error {
    text-align: center;
    padding: var(--space-8);
    background: var(--bg-primary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-xl);
    margin: var(--space-4) 0;
}

.api-key-prompt {
    border-color: var(--warning);
    background: rgba(245, 158, 11, 0.05);
}

.error {
    border-color: var(--danger);
    background: rgba(239, 68, 68, 0.05);
}

.api-key-prompt p:first-child,
.error p:first-child {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.api-key-prompt p:first-child {
    color: var(--warning);
}

.error p:first-child {
    color: var(--danger);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 320px 1fr;
    }

    .control-panel {
        padding: var(--space-4);
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .control-panel {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        max-height: 50vh;
    }

    .navbar {
        padding: var(--space-3) var(--space-4);
        flex-direction: column;
        gap: var(--space-3);
    }

    .nav-controls {
        width: 100%;
        justify-content: center;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .welcome-steps {
        gap: var(--space-3);
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .modal-content {
        margin: 2vh auto;
        width: 95%;
    }

    .results-table {
        font-size: var(--font-size-sm);
    }

    .results-table th,
    .results-table td {
        padding: var(--space-2) var(--space-3);
    }
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Results Table */
.results-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.results-table th,
.results-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--text-color);
}

.results-table tr.arbitrage {
    background: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.roi-positive {
    color: var(--success-color);
    font-weight: 600;
}

.roi-neutral {
    color: var(--secondary-color);
}

.stake-plan {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.stake-plan-item {
    margin-bottom: 0.25rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group small {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
}

.toast {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Loading and Empty States */
.loading {
    text-align: center;
    color: var(--secondary-color);
    padding: 2rem;
}

.empty-state {
    text-align: center;
    color: var(--secondary-color);
    padding: 3rem;
}

.api-key-prompt {
    text-align: center;
    padding: 2rem;
    background: var(--card-bg);
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    margin: 1rem 0;
}

.api-key-prompt p:first-child {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--warning-color);
    margin-bottom: 0.5rem;
}

.api-key-prompt p:last-of-type {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.error {
    text-align: center;
    padding: 2rem;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: 8px;
    margin: 1rem 0;
}

.error p:first-child {
    font-weight: 600;
    color: var(--danger-color);
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .sidebar {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-controls {
        width: 100%;
        justify-content: center;
    }
    
    .results-table {
        font-size: 0.8rem;
    }
    
    .results-table th,
    .results-table td {
        padding: 0.5rem;
    }
}
