/* CSS Variables for theming */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --bg-color: #ffffff;
    --text-color: #212529;
    --border-color: #dee2e6;
    --sidebar-bg: #f8f9fa;
    --card-bg: #ffffff;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #e9ecef;
    --border-color: #495057;
    --sidebar-bg: #2d3748;
    --card-bg: #2d3748;
    --shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Roboto, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Layout */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 0;
}

.sidebar {
    background: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.results-panel {
    padding: 1.5rem;
    overflow-y: auto;
}

.results-header {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h2 {
    color: var(--text-color);
    font-size: 1.5rem;
}

.status-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Results Table */
.results-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.results-table th,
.results-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--text-color);
}

.results-table tr.arbitrage {
    background: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.roi-positive {
    color: var(--success-color);
    font-weight: 600;
}

.roi-neutral {
    color: var(--secondary-color);
}

.stake-plan {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.stake-plan-item {
    margin-bottom: 0.25rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group small {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
}

.toast {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Loading and Empty States */
.loading {
    text-align: center;
    color: var(--secondary-color);
    padding: 2rem;
}

.empty-state {
    text-align: center;
    color: var(--secondary-color);
    padding: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .sidebar {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-controls {
        width: 100%;
        justify-content: center;
    }
    
    .results-table {
        font-size: 0.8rem;
    }
    
    .results-table th,
    .results-table td {
        padding: 0.5rem;
    }
}
